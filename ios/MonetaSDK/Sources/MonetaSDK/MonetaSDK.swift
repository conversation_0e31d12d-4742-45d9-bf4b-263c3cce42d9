import Foundation
import CryptoKit
#if canImport(UIKit)
import UIKit
#endif

public class MonetaSDK {
    public static let shared = MonetaSDK()
    
    private var baseUrl: String?
    private var urlSession: URLSession?
    private var secureStorage: SecureStorage?
    private var signatureGenerator: SignatureGenerator?
    private var isFullyInitialized: Bool = false

    // Internal clients
    private var membershipOrgClient: InternalMembershipOrgClient?
    private var monetaCoreClient: InternalMonetaCoreClient?
    private var uaClient: InternalUAClient?

    // Storage keys
    private let moInfoCurrencyKey = "mo_info_currency"
    private let moInfoRegionKey = "mo_info_region"
    private let initializationStateKey = "sdk_initialization_state"
    
    private init() { }
    
    public func initialize(baseUrl: String) async throws {
        guard self.baseUrl == nil else {
            // Already initialized
            return
        }

        // Validate base URL
        try validateBaseUrl(baseUrl)

        self.baseUrl = baseUrl
        
        // Initialize secure storage
        self.secureStorage = SecureStorage()
        
        // Initialize URL session with configuration
        let config = URLSessionConfiguration.default
        config.timeoutIntervalForRequest = 30.0
        self.urlSession = URLSession(configuration: config)
        
        // Initialize signature generator
        guard let secureStorage = self.secureStorage else {
            return
        }
        self.signatureGenerator = SignatureGenerator(secureStorage: secureStorage)
        
        // Initialize network client
        guard let urlSession = self.urlSession,
              let signatureGenerator = self.signatureGenerator else {
            return
        }
        let networkClient = InternalNetworkClient(session: urlSession)
        
        // Initialize internal clients with appropriate base URLs
        self.membershipOrgClient = InternalMembershipOrgClient(
            baseUrl: "\(baseUrl)/api/mo",
            networkClient: networkClient,
            signatureGenerator: signatureGenerator
        )
        
        self.monetaCoreClient = InternalMonetaCoreClient(
            baseUrl: "\(baseUrl)/users",
            networkClient: networkClient,
            signatureGenerator: signatureGenerator
        )

        self.uaClient = InternalUAClient(
            baseUrl: "\(baseUrl)/recommendations",
            networkClient: networkClient,
            signatureGenerator: signatureGenerator
        )

        // Perform fetchMoInfo and save data to complete initialization
        try await performInitialMoInfoFetch()
    }
    
    private func checkInitialized() throws {
        guard baseUrl != nil,
              membershipOrgClient != nil,
              monetaCoreClient != nil,
              uaClient != nil,
              isFullyInitialized else {
            throw MonetaSDKError.notInitialized
        }
    }

    // MARK: - URL Validation

    private func validateBaseUrl(_ url: String) throws {
        guard let urlComponents = URLComponents(string: url),
              let scheme = urlComponents.scheme,
              scheme.lowercased() == "http" || scheme.lowercased() == "https",
              urlComponents.host != nil else {
            throw MonetaSDKError.networkError("Invalid base URL format. Must be a valid HTTP or HTTPS URL.")
        }
    }

    // MARK: - MO Info Initialization

    private func performInitialMoInfoFetch() async throws {
        guard let membershipOrgClient = self.membershipOrgClient,
              let secureStorage = self.secureStorage else {
            throw MonetaSDKError.notInitialized
        }

        do {
            let response = try await membershipOrgClient.fetchMoInfo()

            if response.success, let data = response.data {
                // Parse the MO info data
                let jsonData = try JSONSerialization.data(withJSONObject: data.value)
                let moInfo = try JSONDecoder().decode(MoInfoResponse.self, from: jsonData)

                // Save MO info to secure storage
                try secureStorage.saveData(moInfoCurrencyKey, value: moInfo.currencyCode)
                try secureStorage.saveData(moInfoRegionKey, value: moInfo.region)
                try secureStorage.saveData(initializationStateKey, value: "completed")

                // Mark as fully initialized
                self.isFullyInitialized = true
            } else {
                throw MonetaSDKError.apiError(response.status, response.message ?? "Failed to fetch MO info")
            }
        } catch {
            // Clean up partial initialization on failure
            self.baseUrl = nil
            self.membershipOrgClient = nil
            self.monetaCoreClient = nil
            self.uaClient = nil
            throw error
        }
    }

    // MARK: - MO Info Access Methods

    public func getMoInfoCurrency() throws -> String? {
        guard let secureStorage = self.secureStorage else {
            throw MonetaSDKError.notInitialized
        }
        return try secureStorage.getData(moInfoCurrencyKey)
    }

    public func getMoInfoRegion() throws -> String? {
        guard let secureStorage = self.secureStorage else {
            throw MonetaSDKError.notInitialized
        }
        return try secureStorage.getData(moInfoRegionKey)
    }

    public func isSDKFullyInitialized() -> Bool {
        return isFullyInitialized
    }
    
    // MARK: - Public API Methods
    
    // Onboarding
    public func startOnboarding() async throws -> OnboardVerificationResponse {
        try checkInitialized()
        guard let membershipOrgClient = self.membershipOrgClient,
              let secureStorage = self.secureStorage else {
            throw MonetaSDKError.notInitialized
        }
        
        // Generate RSA key pair
        let keyPair = try generateKeyPair()
        
        // Store private key securely
        let privateKeyData = keyPair.privateKey.derRepresentation
        let privateKeyEncoded = privateKeyData.base64EncodedString()
        try secureStorage.saveData("private_key", value: privateKeyEncoded)
        
        // Create device token (in a real app, this would be an APNS token or similar)
        let deviceToken = generateDeviceToken()
        try secureStorage.saveData("device_token", value: deviceToken)
        
        // Create onboarding request
        #if canImport(UIKit)
        let deviceName = UIDevice.current.name
        #else
        let deviceName = "Unknown Device"
        #endif

        let request = OnboardingRequest(
            deviceName: deviceName,
            deviceToken: deviceToken,
            publicKey: keyPair.publicKey.derRepresentation.base64EncodedString()
        )
        
        // Make API call
        let response = try await membershipOrgClient.startOnboarding(request: request)
        
        guard response.success, let data = response.data else {
            throw MonetaSDKError.apiError(400, response.message ?? "Unknown error")
        }
        
        return data
    }
    
    public func completeOnboarding() async throws -> OnboardUserResponse {
        try checkInitialized()
        guard let membershipOrgClient = self.membershipOrgClient,
              let secureStorage = self.secureStorage else {
            throw MonetaSDKError.notInitialized
        }
        
        let response = try await membershipOrgClient.completeOnboarding()
        
        guard response.success, let data = response.data else {
            throw MonetaSDKError.apiError(400, response.message ?? "Unknown error")
        }
        
        // Store onboarding data
        let encoder = JSONEncoder()
        let onboardingData = try encoder.encode(data)
        try secureStorage.saveData("onboarding_data", value: String(data: onboardingData, encoding: .utf8)!)
        
        return data
    }
    
    // Transaction methods
    public func getTransactions(page: Int, size: Int) async throws -> PaginatedResponse<TransactionResponse> {
        try checkInitialized()
        guard let monetaCoreClient = self.monetaCoreClient else {
            throw MonetaSDKError.notInitialized
        }
        
        let response = try await monetaCoreClient.getTransactions(page: page, size: size)
        
        guard response.success, let data = response.data else {
            throw MonetaSDKError.apiError(400, response.message ?? "Unknown error")
        }
        
        return data
    }
    
    public func getTransaction(transactionId: String) async throws -> TransactionResponse {
        try checkInitialized()
        guard let monetaCoreClient = self.monetaCoreClient else {
            throw MonetaSDKError.notInitialized
        }
        
        let response = try await monetaCoreClient.getTransaction(transactionId: transactionId)
        
        guard response.success, let data = response.data else {
            throw MonetaSDKError.apiError(400, response.message ?? "Unknown error")
        }
        
        return data
    }
    
    public func approveSubscriptionCharge(transactionId: String, status: String) async throws -> TransactionResponse {
        try checkInitialized()
        guard let monetaCoreClient = self.monetaCoreClient else {
            throw MonetaSDKError.notInitialized
        }
        
        let request = ApprovalSubscriptionChargeRequest(status: status)
        let response = try await monetaCoreClient.approveSubscriptionCharge(
            transactionId: transactionId,
            request: request
        )
        
        guard response.success, let data = response.data else {
            throw MonetaSDKError.apiError(400, response.message ?? "Unknown error")
        }
        
        return data
    }
    
    public func createDispute(transactionId: String, reason: String, itemId: String? = nil) async throws -> Bool {
        try checkInitialized()
        guard let monetaCoreClient = self.monetaCoreClient else {
            throw MonetaSDKError.notInitialized
        }
        
        let request = CreateDisputeRequest(
            transactionId: transactionId,
            reason: reason,
            itemId: itemId
        )
        
        let response = try await monetaCoreClient.createDispute(request: request)
        
        return response.success
    }
    
    // UA Client methods
    public func getRecommendations(userId: String, userProfile: UserProfile, offset: Int, limit: Int) async throws -> [AnyCodable] {
        try checkInitialized()
        guard let uaClient = self.uaClient else {
            throw MonetaSDKError.notInitialized
        }
        
        let request = RecommendationsRequest(
            userId: userId,
            userProfile: userProfile,
            offset: offset,
            limit: limit
        )
        
        let response = try await uaClient.getRecommendations(request: request)
        
        guard response.success, let data = response.data else {
            throw MonetaSDKError.apiError(400, response.message ?? "Unknown error")
        }
        
        return data
    }
    
    public func updateUserProfile(userId: String, contentPreferences: UAUserContentPreferences) async throws -> Bool {
        try checkInitialized()
        guard let uaClient = self.uaClient else {
            throw MonetaSDKError.notInitialized
        }

        let request = UpdateUAUserProfileRequest(
            userId: userId,
            contentPreferences: contentPreferences
        )
        
        let response = try await uaClient.updateUserProfile(request: request)
        
        return response.success
    }
    
    // Publisher authentication
    public func authPublisher(qrCodeId: String, session: String) async throws -> OnboardUserResponse {
        try checkInitialized()
        guard let membershipOrgClient = self.membershipOrgClient else {
            throw MonetaSDKError.notInitialized
        }
        
        let response = try await membershipOrgClient.publisherLogin(session: session)
        
        guard response.success, let data = response.data else {
            throw MonetaSDKError.apiError(400, response.message ?? "Unknown error")
        }
        
        return data
    }

    // ========================================
    // PRD-COMPLIANT PUBLIC API METHODS
    // ========================================

    // MARK: - MembershipOrgClient Endpoints (PRD Section 4.3.1)

    public func fetchMoInfo() async throws -> ApiResponse<AnyCodable> {
        try checkInitialized()
        guard let membershipOrgClient = self.membershipOrgClient else {
            throw MonetaSDKError.notInitialized
        }

        return try await membershipOrgClient.fetchMoInfo()
    }

    public func verifyOnboarding(requestId: String, request: OnboardingRequest) async throws -> ApiResponse<OnboardVerificationResponse> {
        try checkInitialized()
        guard let membershipOrgClient = self.membershipOrgClient else {
            throw MonetaSDKError.notInitialized
        }

        return try await membershipOrgClient.verifyOnboarding(requestId: requestId, request: request)
    }

    public func checkUserCodeIsVerified(requestId: String, deviceCode: String) async throws -> ApiResponse<OnboardUserResponse> {
        try checkInitialized()
        guard let membershipOrgClient = self.membershipOrgClient else {
            throw MonetaSDKError.notInitialized
        }

        return try await membershipOrgClient.checkUserCodeIsVerified(requestId: requestId, deviceCode: deviceCode)
    }

    public func fetchTransactions(
        month: String? = nil,
        pageSize: Int? = nil,
        pageBefore: String? = nil,
        pageAfter: String? = nil
    ) async throws -> PaginatedResponse<TransactionResponse> {
        try checkInitialized()
        guard let membershipOrgClient = self.membershipOrgClient else {
            throw MonetaSDKError.notInitialized
        }

        let response = try await membershipOrgClient.fetchTransactions(
            month: month,
            pageSize: pageSize,
            pageBefore: pageBefore,
            pageAfter: pageAfter
        )

        guard response.success, let data = response.data else {
            throw MonetaSDKError.apiError(400, response.message ?? "Unknown error")
        }

        return data
    }

    public func getTransaction(id: String) async throws -> ApiResponse<TransactionResponse> {
        try checkInitialized()
        guard let membershipOrgClient = self.membershipOrgClient else {
            throw MonetaSDKError.notInitialized
        }

        return try await membershipOrgClient.getTransaction(id: id)
    }

    public func getRelatedTransactions(
        id: String,
        pageSize: Int? = nil,
        pageOrder: String? = nil
    ) async throws -> PaginatedResponse<TransactionResponse> {
        try checkInitialized()
        guard let membershipOrgClient = self.membershipOrgClient else {
            throw MonetaSDKError.notInitialized
        }

        let response = try await membershipOrgClient.getRelatedTransactions(
            id: id,
            pageSize: pageSize,
            pageOrder: pageOrder
        )

        guard response.success, let data = response.data else {
            throw MonetaSDKError.apiError(400, response.message ?? "Unknown error")
        }

        return data
    }

    public func fetchUserFeeds(
        pageSize: Int? = nil,
        pageAfter: String? = nil
    ) async throws -> PaginatedResponse<UserFeedResponse> {
        try checkInitialized()
        guard let membershipOrgClient = self.membershipOrgClient else {
            throw MonetaSDKError.notInitialized
        }

        let response = try await membershipOrgClient.fetchUserFeeds(
            pageSize: pageSize,
            pageAfter: pageAfter
        )

        guard response.success, let data = response.data else {
            throw MonetaSDKError.apiError(400, response.message ?? "Unknown error")
        }

        return data
    }

    public func getFeedById(feedId: String) async throws -> ApiResponse<UserFeedResponse> {
        try checkInitialized()
        guard let membershipOrgClient = self.membershipOrgClient else {
            throw MonetaSDKError.notInitialized
        }

        return try await membershipOrgClient.getFeedById(feedId: feedId)
    }

    public func markFeedsAsRead(body: [String: [String]]) async throws -> ApiResponse<AnyCodable> {
        try checkInitialized()
        guard let membershipOrgClient = self.membershipOrgClient else {
            throw MonetaSDKError.notInitialized
        }

        return try await membershipOrgClient.markFeedsAsRead(body: body)
    }

    public func deleteFeeds(body: [String: [String]]) async throws -> ApiResponse<AnyCodable> {
        try checkInitialized()
        guard let membershipOrgClient = self.membershipOrgClient else {
            throw MonetaSDKError.notInitialized
        }

        return try await membershipOrgClient.deleteFeeds(body: body)
    }

    public func deviceRemoval(deviceId: String) async throws -> ApiResponse<AnyCodable> {
        try checkInitialized()
        guard let membershipOrgClient = self.membershipOrgClient else {
            throw MonetaSDKError.notInitialized
        }

        return try await membershipOrgClient.deviceRemoval(deviceId: deviceId)
    }

    public func fetchPolicies() async throws -> ApiResponse<[UserPolicyTypeResponse]> {
        try checkInitialized()
        guard let membershipOrgClient = self.membershipOrgClient else {
            throw MonetaSDKError.notInitialized
        }

        return try await membershipOrgClient.fetchPolicies()
    }

    public func setPolicy(type: String, body: [String: AnyCodable]) async throws -> ApiResponse<UserPolicyDataResponse> {
        try checkInitialized()
        guard let membershipOrgClient = self.membershipOrgClient else {
            throw MonetaSDKError.notInitialized
        }

        return try await membershipOrgClient.setPolicy(type: type, body: body)
    }

    public func fetchIndustries() async throws -> ApiResponse<[IndustryResponse]> {
        try checkInitialized()
        guard let membershipOrgClient = self.membershipOrgClient else {
            throw MonetaSDKError.notInitialized
        }

        return try await membershipOrgClient.fetchIndustries()
    }

    public func fetchPublishers() async throws -> ApiResponse<[IndustryResponse]> {
        try checkInitialized()
        guard let membershipOrgClient = self.membershipOrgClient else {
            throw MonetaSDKError.notInitialized
        }

        return try await membershipOrgClient.fetchPublishers()
    }

    public func processSubscriptionCharge(id: String, request: ApprovalSubscriptionChargeRequest) async throws -> ApiResponse<AnyCodable> {
        try checkInitialized()
        guard let membershipOrgClient = self.membershipOrgClient else {
            throw MonetaSDKError.notInitialized
        }

        return try await membershipOrgClient.processSubscriptionCharge(id: id, request: request)
    }

    public func fetchIncrements(id: String) async throws -> PaginatedResponse<IncrementResponse> {
        try checkInitialized()
        guard let membershipOrgClient = self.membershipOrgClient else {
            throw MonetaSDKError.notInitialized
        }

        let response = try await membershipOrgClient.fetchIncrements(id: id)

        guard response.success, let data = response.data else {
            throw MonetaSDKError.apiError(400, response.message ?? "Unknown error")
        }

        return data
    }

    public func createFetchingConsumptionActivitiesRequest(id: String) async throws -> ApiResponse<ConsumptionActivityStatusResponse> {
        try checkInitialized()
        guard let membershipOrgClient = self.membershipOrgClient else {
            throw MonetaSDKError.notInitialized
        }

        return try await membershipOrgClient.createFetchingConsumptionActivitiesRequest(id: id)
    }

    public func checkStatusFetchingConsumptionActivitiesRequest(id: String, rid: String) async throws -> ApiResponse<ConsumptionActivityStatusResponse> {
        try checkInitialized()
        guard let membershipOrgClient = self.membershipOrgClient else {
            throw MonetaSDKError.notInitialized
        }

        return try await membershipOrgClient.checkStatusFetchingConsumptionActivitiesRequest(id: id, rid: rid)
    }

    public func fetchConsumptionActivities(id: String) async throws -> ApiResponse<[ConsumptionActivityResponse]> {
        try checkInitialized()
        guard let membershipOrgClient = self.membershipOrgClient else {
            throw MonetaSDKError.notInitialized
        }

        return try await membershipOrgClient.fetchConsumptionActivities(id: id)
    }

    public func createDisputeRequest(request: CreateDisputeRequest) async throws -> ApiResponse<DisputeRequestResponse> {
        try checkInitialized()
        guard let membershipOrgClient = self.membershipOrgClient else {
            throw MonetaSDKError.notInitialized
        }

        return try await membershipOrgClient.createDisputeRequest(request: request)
    }

    public func fetchDisputes(transactionId: String? = nil) async throws -> ApiResponse<[DisputeRequestResponse]> {
        try checkInitialized()
        guard let membershipOrgClient = self.membershipOrgClient else {
            throw MonetaSDKError.notInitialized
        }

        return try await membershipOrgClient.fetchDisputes(transactionId: transactionId)
    }

    public func fetchLatestSettlementBillingCycle() async throws -> ApiResponse<UserBillingCyclesResponse> {
        try checkInitialized()
        guard let membershipOrgClient = self.membershipOrgClient else {
            throw MonetaSDKError.notInitialized
        }

        return try await membershipOrgClient.fetchLatestSettlementBillingCycle()
    }

    public func fetchSettlementBillingCycle(
        billingFrom: String? = nil,
        billingTo: String? = nil
    ) async throws -> ApiResponse<[UserBillingCyclesResponse]> {
        try checkInitialized()
        guard let membershipOrgClient = self.membershipOrgClient else {
            throw MonetaSDKError.notInitialized
        }

        return try await membershipOrgClient.fetchSettlementBillingCycle(
            billingFrom: billingFrom,
            billingTo: billingTo
        )
    }

    public func fetchSettlementBillingCycleConsumption(code: String) async throws -> ApiResponse<[ConsumptionResponse]> {
        try checkInitialized()
        guard let membershipOrgClient = self.membershipOrgClient else {
            throw MonetaSDKError.notInitialized
        }

        return try await membershipOrgClient.fetchSettlementBillingCycleConsumption(code: code)
    }

    public func fetchBalance() async throws -> ApiResponse<UserBalanceResponse> {
        try checkInitialized()
        guard let membershipOrgClient = self.membershipOrgClient else {
            throw MonetaSDKError.notInitialized
        }

        return try await membershipOrgClient.fetchBalance()
    }

    public func fetchDebitHistory() async throws -> ApiResponse<AnyCodable> {
        try checkInitialized()
        guard let membershipOrgClient = self.membershipOrgClient else {
            throw MonetaSDKError.notInitialized
        }

        return try await membershipOrgClient.fetchDebitHistory()
    }

    // MARK: - MonetaCoreClient Endpoints (PRD Section 4.3.2)

    public func authPublisher(qrCodeId: String, request: PublisherLoginRequest) async throws -> PublisherLoginResponse {
        try checkInitialized()
        guard let monetaCoreClient = self.monetaCoreClient else {
            throw MonetaSDKError.notInitialized
        }

        let response = try await monetaCoreClient.authPublisher(qrCodeId: qrCodeId, request: request)

        guard response.success, let data = response.data else {
            throw MonetaSDKError.apiError(400, response.message ?? "Unknown error")
        }

        return data
    }

    public func checkPublisherLoginStatus(qrCodeId: String) async throws -> PublisherLoginResponse {
        try checkInitialized()
        guard let monetaCoreClient = self.monetaCoreClient else {
            throw MonetaSDKError.notInitialized
        }

        let response = try await monetaCoreClient.checkPublisherLoginStatus(qrCodeId: qrCodeId)

        guard response.success, let data = response.data else {
            throw MonetaSDKError.apiError(400, response.message ?? "Unknown error")
        }

        return data
    }

    public func fetchArticles(
        categories: String? = nil,
        before: String? = nil
    ) async throws -> [ArticleResponse] {
        try checkInitialized()
        guard let monetaCoreClient = self.monetaCoreClient else {
            throw MonetaSDKError.notInitialized
        }

        let response = try await monetaCoreClient.fetchArticles(categories: categories, before: before)

        guard response.success, let data = response.data else {
            throw MonetaSDKError.apiError(400, response.message ?? "Unknown error")
        }

        return data
    }

    public func fetchArticleCategories() async throws -> [String] {
        try checkInitialized()
        guard let monetaCoreClient = self.monetaCoreClient else {
            throw MonetaSDKError.notInitialized
        }

        let response = try await monetaCoreClient.fetchArticleCategories()

        guard response.success, let data = response.data else {
            throw MonetaSDKError.apiError(400, response.message ?? "Unknown error")
        }

        return data
    }

    // MARK: - UAClient Endpoints (PRD Section 4.3.3)

    public func fetchRssContent(
        userId: String,
        limit: Int? = nil,
        offset: Int? = nil
    ) async throws -> UAResponse {
        try checkInitialized()
        guard let uaClient = self.uaClient else {
            throw MonetaSDKError.notInitialized
        }

        let response = try await uaClient.fetchRssContent(userId: userId, limit: limit, offset: offset)

        guard response.success, let data = response.data else {
            throw MonetaSDKError.apiError(400, response.message ?? "Unknown error")
        }

        return data
    }

    public func fetchRecommendations(request: RecommendationsRequest) async throws -> UAResponse {
        try checkInitialized()
        guard let uaClient = self.uaClient else {
            throw MonetaSDKError.notInitialized
        }

        let response = try await uaClient.fetchRecommendations(request: request)

        guard response.success, let data = response.data else {
            throw MonetaSDKError.apiError(400, response.message ?? "Unknown error")
        }

        return data
    }

    public func fetchInterests() async throws -> InterestsResponse {
        try checkInitialized()
        guard let uaClient = self.uaClient else {
            throw MonetaSDKError.notInitialized
        }

        let response = try await uaClient.fetchInterests()

        guard response.success, let data = response.data else {
            throw MonetaSDKError.apiError(400, response.message ?? "Unknown error")
        }

        return data
    }

    public func fetchUserProfile(userId: String) async throws -> UAUserProfileResponse {
        try checkInitialized()
        guard let uaClient = self.uaClient else {
            throw MonetaSDKError.notInitialized
        }

        let response = try await uaClient.fetchUserProfile(userId: userId)

        guard response.success, let data = response.data else {
            throw MonetaSDKError.apiError(400, response.message ?? "Unknown error")
        }

        return data
    }

    public func updateUserProfile(request: UpdateUAUserProfileRequest) async throws -> UpdateUAUserProfileResponse {
        try checkInitialized()
        guard let uaClient = self.uaClient else {
            throw MonetaSDKError.notInitialized
        }

        let response = try await uaClient.updateUserProfile(request: request)

        guard response.success, let data = response.data else {
            throw MonetaSDKError.apiError(400, response.message ?? "Unknown error")
        }

        return data
    }

    // Helper methods
    private func generateKeyPair() throws -> (privateKey: SecKey, publicKey: SecKey) {
        let attributes: [String: Any] = [
            kSecAttrKeyType as String: kSecAttrKeyTypeRSA,
            kSecAttrKeySizeInBits as String: 2048
        ]
        
        var error: Unmanaged<CFError>?
        guard let privateKey = SecKeyCreateRandomKey(attributes as CFDictionary, &error) else {
            throw MonetaSDKError.cryptoError("Failed to generate key pair: \(error?.takeRetainedValue().localizedDescription ?? "unknown error")")
        }
        
        guard let publicKey = SecKeyCopyPublicKey(privateKey) else {
            throw MonetaSDKError.cryptoError("Failed to extract public key")
        }
        
        return (privateKey, publicKey)
    }
    
    private func generateDeviceToken() -> String {
        // In a real implementation, this would be an APNS token or similar
        // For demo purposes, generate a random string
        let bytes = [UInt8](repeating: 0, count: 32)
        let data = Data(bytes.map { _ in UInt8.random(in: 0...255) })
        return data.base64EncodedString()
    }
}
