// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		5F776F352DF7134F00A4A8BA /* MonetaSDK in Frameworks */ = {isa = PBXBuildFile; productRef = 5F776F342DF7134F00A4A8BA /* MonetaSDK */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		5F6240772DA7B976004BE30C /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 5F6240602DA7B975004BE30C /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 5F6240672DA7B975004BE30C;
			remoteInfo = MonetaSample;
		};
		5F6240812DA7B976004BE30C /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 5F6240602DA7B975004BE30C /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 5F6240672DA7B975004BE30C;
			remoteInfo = MonetaSample;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		5F6240682DA7B975004BE30C /* MonetaSample.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = MonetaSample.app; sourceTree = BUILT_PRODUCTS_DIR; };
		5F6240762DA7B976004BE30C /* MonetaSampleTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = MonetaSampleTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		5F6240802DA7B976004BE30C /* MonetaSampleUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = MonetaSampleUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedBuildFileExceptionSet section */
		5FC6754A2DDD5D3300FFE94F /* Exceptions for "MonetaSample" folder in "MonetaSample" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				Info.plist,
			);
			target = 5F6240672DA7B975004BE30C /* MonetaSample */;
		};
/* End PBXFileSystemSynchronizedBuildFileExceptionSet section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		5F62406A2DA7B975004BE30C /* MonetaSample */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			exceptions = (
				5FC6754A2DDD5D3300FFE94F /* Exceptions for "MonetaSample" folder in "MonetaSample" target */,
			);
			path = MonetaSample;
			sourceTree = "<group>";
		};
		5F6240792DA7B976004BE30C /* MonetaSampleTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = MonetaSampleTests;
			sourceTree = "<group>";
		};
		5F6240832DA7B976004BE30C /* MonetaSampleUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = MonetaSampleUITests;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		5F6240652DA7B975004BE30C /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				5F776F352DF7134F00A4A8BA /* MonetaSDK in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		5F6240732DA7B976004BE30C /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		5F62407D2DA7B976004BE30C /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		5F62405F2DA7B975004BE30C = {
			isa = PBXGroup;
			children = (
				5F62406A2DA7B975004BE30C /* MonetaSample */,
				5F6240792DA7B976004BE30C /* MonetaSampleTests */,
				5F6240832DA7B976004BE30C /* MonetaSampleUITests */,
				5F6240932DA7B9ED004BE30C /* Frameworks */,
				5F6240692DA7B975004BE30C /* Products */,
			);
			sourceTree = "<group>";
		};
		5F6240692DA7B975004BE30C /* Products */ = {
			isa = PBXGroup;
			children = (
				5F6240682DA7B975004BE30C /* MonetaSample.app */,
				5F6240762DA7B976004BE30C /* MonetaSampleTests.xctest */,
				5F6240802DA7B976004BE30C /* MonetaSampleUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		5F6240932DA7B9ED004BE30C /* Frameworks */ = {
			isa = PBXGroup;
			children = (
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		5F6240672DA7B975004BE30C /* MonetaSample */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 5F62408A2DA7B976004BE30C /* Build configuration list for PBXNativeTarget "MonetaSample" */;
			buildPhases = (
				5F6240642DA7B975004BE30C /* Sources */,
				5F6240652DA7B975004BE30C /* Frameworks */,
				5F6240662DA7B975004BE30C /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				5F62406A2DA7B975004BE30C /* MonetaSample */,
			);
			name = MonetaSample;
			packageProductDependencies = (
				5F776F342DF7134F00A4A8BA /* MonetaSDK */,
			);
			productName = MonetaSample;
			productReference = 5F6240682DA7B975004BE30C /* MonetaSample.app */;
			productType = "com.apple.product-type.application";
		};
		5F6240752DA7B976004BE30C /* MonetaSampleTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 5F62408D2DA7B976004BE30C /* Build configuration list for PBXNativeTarget "MonetaSampleTests" */;
			buildPhases = (
				5F6240722DA7B976004BE30C /* Sources */,
				5F6240732DA7B976004BE30C /* Frameworks */,
				5F6240742DA7B976004BE30C /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				5F6240782DA7B976004BE30C /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				5F6240792DA7B976004BE30C /* MonetaSampleTests */,
			);
			name = MonetaSampleTests;
			packageProductDependencies = (
			);
			productName = MonetaSampleTests;
			productReference = 5F6240762DA7B976004BE30C /* MonetaSampleTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		5F62407F2DA7B976004BE30C /* MonetaSampleUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 5F6240902DA7B976004BE30C /* Build configuration list for PBXNativeTarget "MonetaSampleUITests" */;
			buildPhases = (
				5F62407C2DA7B976004BE30C /* Sources */,
				5F62407D2DA7B976004BE30C /* Frameworks */,
				5F62407E2DA7B976004BE30C /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				5F6240822DA7B976004BE30C /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				5F6240832DA7B976004BE30C /* MonetaSampleUITests */,
			);
			name = MonetaSampleUITests;
			packageProductDependencies = (
			);
			productName = MonetaSampleUITests;
			productReference = 5F6240802DA7B976004BE30C /* MonetaSampleUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		5F6240602DA7B975004BE30C /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1630;
				LastUpgradeCheck = 1630;
				TargetAttributes = {
					5F6240672DA7B975004BE30C = {
						CreatedOnToolsVersion = 16.3;
					};
					5F6240752DA7B976004BE30C = {
						CreatedOnToolsVersion = 16.3;
						TestTargetID = 5F6240672DA7B975004BE30C;
					};
					5F62407F2DA7B976004BE30C = {
						CreatedOnToolsVersion = 16.3;
						TestTargetID = 5F6240672DA7B975004BE30C;
					};
				};
			};
			buildConfigurationList = 5F6240632DA7B975004BE30C /* Build configuration list for PBXProject "MonetaSample" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 5F62405F2DA7B975004BE30C;
			minimizedProjectReferenceProxies = 1;
			packageReferences = (
				5F776F332DF7134F00A4A8BA /* XCLocalSwiftPackageReference "../MonetaSDK" */,
			);
			preferredProjectObjectVersion = 77;
			productRefGroup = 5F6240692DA7B975004BE30C /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				5F6240672DA7B975004BE30C /* MonetaSample */,
				5F6240752DA7B976004BE30C /* MonetaSampleTests */,
				5F62407F2DA7B976004BE30C /* MonetaSampleUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		5F6240662DA7B975004BE30C /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		5F6240742DA7B976004BE30C /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		5F62407E2DA7B976004BE30C /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		5F6240642DA7B975004BE30C /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		5F6240722DA7B976004BE30C /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		5F62407C2DA7B976004BE30C /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		5F6240782DA7B976004BE30C /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 5F6240672DA7B975004BE30C /* MonetaSample */;
			targetProxy = 5F6240772DA7B976004BE30C /* PBXContainerItemProxy */;
		};
		5F6240822DA7B976004BE30C /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 5F6240672DA7B975004BE30C /* MonetaSample */;
			targetProxy = 5F6240812DA7B976004BE30C /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		5F6240882DA7B976004BE30C /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MACOSX_DEPLOYMENT_TARGET = 15.4;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = auto;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		5F6240892DA7B976004BE30C /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MACOSX_DEPLOYMENT_TARGET = 15.4;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = auto;
				SWIFT_COMPILATION_MODE = wholemodule;
			};
			name = Release;
		};
		5F62408B2DA7B976004BE30C /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = MonetaSample/MonetaSample.entitlements;
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = MonetaSample/Info.plist;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				INFOPLIST_KEY_UIStatusBarHidden = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.moneta.sample.app.MonetaSample;
				PRODUCT_NAME = "$(TARGET_NAME)";
				REGISTER_APP_GROUPS = YES;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx";
				SUPPORTS_MACCATALYST = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		5F62408C2DA7B976004BE30C /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = MonetaSample/MonetaSample.entitlements;
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = MonetaSample/Info.plist;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				INFOPLIST_KEY_UIStatusBarHidden = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.moneta.sample.app.MonetaSample;
				PRODUCT_NAME = "$(TARGET_NAME)";
				REGISTER_APP_GROUPS = YES;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx";
				SUPPORTS_MACCATALYST = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		5F62408E2DA7B976004BE30C /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MACOSX_DEPLOYMENT_TARGET = 15.4;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.moneta.sample.app.MonetaSampleTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/MonetaSample.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/MonetaSample";
			};
			name = Debug;
		};
		5F62408F2DA7B976004BE30C /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MACOSX_DEPLOYMENT_TARGET = 15.4;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.moneta.sample.app.MonetaSampleTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/MonetaSample.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/MonetaSample";
			};
			name = Release;
		};
		5F6240912DA7B976004BE30C /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.moneta.sample.app.MonetaSampleUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TEST_TARGET_NAME = MonetaSample;
			};
			name = Debug;
		};
		5F6240922DA7B976004BE30C /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.moneta.sample.app.MonetaSampleUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TEST_TARGET_NAME = MonetaSample;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		5F6240632DA7B975004BE30C /* Build configuration list for PBXProject "MonetaSample" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				5F6240882DA7B976004BE30C /* Debug */,
				5F6240892DA7B976004BE30C /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		5F62408A2DA7B976004BE30C /* Build configuration list for PBXNativeTarget "MonetaSample" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				5F62408B2DA7B976004BE30C /* Debug */,
				5F62408C2DA7B976004BE30C /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		5F62408D2DA7B976004BE30C /* Build configuration list for PBXNativeTarget "MonetaSampleTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				5F62408E2DA7B976004BE30C /* Debug */,
				5F62408F2DA7B976004BE30C /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		5F6240902DA7B976004BE30C /* Build configuration list for PBXNativeTarget "MonetaSampleUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				5F6240912DA7B976004BE30C /* Debug */,
				5F6240922DA7B976004BE30C /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCLocalSwiftPackageReference section */
		5F776F332DF7134F00A4A8BA /* XCLocalSwiftPackageReference "../MonetaSDK" */ = {
			isa = XCLocalSwiftPackageReference;
			relativePath = ../MonetaSDK;
		};
/* End XCLocalSwiftPackageReference section */

/* Begin XCSwiftPackageProductDependency section */
		5F776F342DF7134F00A4A8BA /* MonetaSDK */ = {
			isa = XCSwiftPackageProductDependency;
			productName = MonetaSDK;
		};
/* End XCSwiftPackageProductDependency section */
	};
	rootObject = 5F6240602DA7B975004BE30C /* Project object */;
}
