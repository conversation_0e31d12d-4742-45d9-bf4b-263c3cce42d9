import Foundation

extension String {
    /// Returns a localized string for the given key
    var localized: String {
        return NSLocalizedString(self, comment: "")
    }
    
    /// Returns a localized string with format arguments
    func localized(with arguments: CVarArg...) -> String {
        return String(format: NSLocalizedString(self, comment: ""), arguments: arguments)
    }
}

// MARK: - Localization Keys
struct LocalizedKeys {
    
    // MARK: - Navigation
    struct Navigation {
        static let dashboard = "nav.dashboard"
        static let transactions = "nav.transactions"
        static let recommendations = "nav.recommendations"
        static let publisherAuth = "nav.publisher_auth"
        static let settings = "nav.settings"
    }
    
    // MARK: - Onboarding
    struct Onboarding {
        static let title = "onboarding.title"
        static let subtitle = "onboarding.subtitle"
        static let startButton = "onboarding.start_button"
        static let completeButton = "onboarding.complete_button"
        static let userCodeLabel = "onboarding.user_code_label"
        static let deviceCodeLabel = "onboarding.device_code_label"
        static let instructions = "onboarding.instructions"
        static let completeSetup = "onboarding.complete_setup"
    }
    
    // MARK: - Dashboard
    struct Dashboard {
        static let title = "dashboard.title"
        static let balanceLabel = "dashboard.balance_label"
        static let recentTransactions = "dashboard.recent_transactions"
        static let viewAll = "dashboard.view_all"
        static let noTransactions = "dashboard.no_transactions"
    }
    
    // MARK: - Transactions
    struct Transactions {
        static let title = "transactions.title"
        static let noTransactions = "transactions.no_transactions"
        static let emptyMessage = "transactions.empty_message"
        static let loading = "transactions.loading"
        static let loadMoreError = "transactions.load_more_error"
    }
    
    // MARK: - Recommendations
    struct Recommendations {
        static let title = "recommendations.title"
        static let noRecommendations = "recommendations.no_recommendations"
        static let emptyMessage = "recommendations.empty_message"
        static let loading = "recommendations.loading"
        static let learnMore = "recommendations.learn_more"
        static let match = "recommendations.match"
    }
    
    // MARK: - Publisher Auth
    struct PublisherAuth {
        static let title = "publisher_auth.title"
        static let scanButton = "publisher_auth.scan_button"
        static let instructions = "publisher_auth.instructions"
        static let successTitle = "publisher_auth.success_title"
        static let welcome = "publisher_auth.welcome"
        static let continueButton = "publisher_auth.continue"
        static let authenticating = "publisher_auth.authenticating"
    }
    
    // MARK: - Settings
    struct Settings {
        static let title = "settings.title"
        static let userProfile = "settings.user_profile"
        static let contentPreferences = "settings.content_preferences"
        static let interests = "settings.interests"
        static let notifications = "settings.notifications"
        static let privacyPolicies = "settings.privacy_policies"
        static let signOut = "settings.sign_out"
        static let version = "settings.version"
        static let privacyPolicy = "settings.privacy_policy"
        static let termsOfService = "settings.terms_of_service"
        static let signOutConfirmation = "settings.sign_out_confirmation"
        static let loadingProfile = "settings.loading_profile"
    }
    
    // MARK: - Common
    struct Common {
        static let loading = "common.loading"
        static let error = "common.error"
        static let ok = "common.ok"
        static let cancel = "common.cancel"
        static let retry = "common.retry"
        static let save = "common.save"
        static let done = "common.done"
        static let continueButton = "common.continue"
    }
    
    // MARK: - Errors
    struct Errors {
        static let network = "error.network"
        static let sdkNotInitialized = "error.sdk_not_initialized"
        static let onboardingFailed = "error.onboarding_failed"
        static let authenticationFailed = "error.authentication_failed"
        static let loadDashboard = "error.load_dashboard"
        static let loadTransactions = "error.load_transactions"
        static let loadRecommendations = "error.load_recommendations"
        static let loadProfile = "error.load_profile"
    }
    
    // MARK: - Transaction Status
    struct TransactionStatus {
        static let completed = "transaction.status.completed"
        static let pending = "transaction.status.pending"
        static let failed = "transaction.status.failed"
        static let cancelled = "transaction.status.cancelled"
    }
    
    // MARK: - Currency
    struct Currency {
        static let pendingFormat = "currency.pending_format"
    }
    
    // MARK: - Permissions
    struct Permissions {
        static let cameraTitle = "permission.camera.title"
        static let cameraMessage = "permission.camera.message"
        static let settings = "permission.camera.settings"
    }
    
    // MARK: - QR Scanner
    struct QRScanner {
        static let prompt = "qr_scanner.prompt"
        static let error = "qr_scanner.error"
    }
    
    // MARK: - Accessibility
    struct Accessibility {
        static let balanceCard = "accessibility.balance_card"
        static let transactionRow = "accessibility.transaction_row"
        static let recommendationCard = "accessibility.recommendation_card"
        static let scanButton = "accessibility.scan_button"
        static let tabDashboard = "accessibility.tab_dashboard"
        static let tabTransactions = "accessibility.tab_transactions"
        static let tabRecommendations = "accessibility.tab_recommendations"
        static let tabPublisherAuth = "accessibility.tab_publisher_auth"
        static let tabSettings = "accessibility.tab_settings"
    }
}
