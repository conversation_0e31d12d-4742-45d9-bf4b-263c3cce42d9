import Foundation

// Model for UA Recommendations used in the sample app
public struct UARecommendation: Codable, Identifiable {
    public let id: String
    public let title: String
    public let description: String
    public let url: String
    public let source: String
    public let score: Double
    
    public init(id: String, title: String, description: String, url: String, source: String, score: Double) {
        self.id = id
        self.title = title
        self.description = description
        self.url = url
        self.source = source
        self.score = score
    }
    
    enum CodingKeys: String, CodingKey {
        case id, title, description, url, source, score
    }
}
