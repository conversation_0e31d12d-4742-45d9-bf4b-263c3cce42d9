import SwiftUI
import Foundation

#if canImport(UIKit)
import UIKit
#endif

#if canImport(AppKit)
import AppKit
#endif

// MARK: - Platform Detection
enum Platform {
    case iOS
    case macOS
    
    static var current: Platform {
        #if os(iOS)
        return .iOS
        #elseif os(macOS)
        return .macOS
        #endif
    }
}

// MARK: - Platform Service Protocol
protocol PlatformServiceProtocol {
    func openURL(_ url: URL)
    func backgroundColor() -> Color
    func secondaryBackgroundColor() -> Color
    func canScanQRCode() -> Bool
}

// MARK: - Platform Service Implementation
class PlatformService: PlatformServiceProtocol {
    static let shared = PlatformService()
    
    private init() {}
    
    func openURL(_ url: URL) {
        #if os(iOS)
        if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
           let window = windowScene.windows.first {
            UIApplication.shared.open(url)
        }
        #elseif os(macOS)
        NSWorkspace.shared.open(url)
        #endif
    }
    
    func backgroundColor() -> Color {
        #if os(iOS)
        return Color(.systemBackground)
        #elseif os(macOS)
        return Color(NSColor.controlBackgroundColor)
        #endif
    }
    
    func secondaryBackgroundColor() -> Color {
        #if os(iOS)
        return Color(.systemGray6)
        #elseif os(macOS)
        return Color(NSColor.controlBackgroundColor)
        #endif
    }
    
    func canScanQRCode() -> Bool {
        #if os(iOS)
        return true
        #elseif os(macOS)
        return false
        #endif
    }
}

// MARK: - Platform-Specific View Modifiers
struct PlatformNavigationBarModifier: ViewModifier {
    let isHidden: Bool
    
    func body(content: Content) -> some View {
        #if os(iOS)
        content.navigationBarHidden(isHidden)
        #elseif os(macOS)
        content.navigationBarBackButtonHidden(isHidden)
        #endif
    }
}

struct PlatformNavigationTitleDisplayModeModifier: ViewModifier {
    func body(content: Content) -> some View {
        #if os(iOS)
        content.navigationBarTitleDisplayMode(.inline)
        #elseif os(macOS)
        content
        #endif
    }
}

// MARK: - View Extensions
extension View {
    func platformNavigationBarHidden(_ hidden: Bool) -> some View {
        self.modifier(PlatformNavigationBarModifier(isHidden: hidden))
    }
    
    func platformNavigationTitleDisplayMode() -> some View {
        self.modifier(PlatformNavigationTitleDisplayModeModifier())
    }
    
    func platformBackground() -> some View {
        self.background(PlatformService.shared.backgroundColor())
    }
    
    func platformSecondaryBackground() -> some View {
        self.background(PlatformService.shared.secondaryBackgroundColor())
    }
}
