import Foundation
import MonetaSDK

@MainActor
class PublisherAuthViewModel: ObservableObject {
    @Published var isLoading = false
    @Published var authResult: OnboardUserResponse?
    @Published var errorMessage: String?
    
    func authenticatePublisher(qrCodeContent: String) async {
        isLoading = true
        errorMessage = nil
        
        do {
            // Parse QR code content to extract session
            // In a real app, this would be more sophisticated parsing
            let session = parseQRCodeForSession(qrCodeContent)

            let userData = try await MonetaSDK.shared.authPublisher(
                qrCodeId: "demo_qr_id",
                session: session
            )

            authResult = userData
            
        } catch {
            errorMessage = "Authentication failed: \(error.localizedDescription)"
        }
        
        isLoading = false
    }
    
    private func parseQRCodeForSession(_ content: String) -> String {
        // Try to parse the QR code content to extract session
        // Expected format: "session_id" or "qrCodeId:session_id"
        let parts = content.split(separator: ":")

        if parts.count >= 2 {
            // If format is "qrCodeId:session", return the session part
            return String(parts[1])
        } else {
            // If it's just a session ID or we need to generate one
            return content.isEmpty ? "demo_session_\(UUID().uuidString.prefix(8))" : content
        }
    }
    
    func clearAuthResult() {
        authResult = nil
    }
    
    func clearError() {
        errorMessage = nil
    }
}
