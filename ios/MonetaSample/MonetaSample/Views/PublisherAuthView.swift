import SwiftUI
import MonetaSDK

struct PublisherAuthView: View {
    @StateObject private var viewModel = PublisherAuthViewModel()
    @State private var showingManualEntry = false
    @State private var qrCodeText = ""

    var body: some View {
        NavigationView {
            VStack(spacing: 24) {
                Spacer()

                if viewModel.isLoading {
                    ProgressView("Authenticating...")
                        .scaleEffect(1.2)
                } else if let authResult = viewModel.authResult {
                    AuthSuccessView(authResult: authResult) {
                        viewModel.clearAuthResult()
                    }
                } else {
                    VStack(spacing: 32) {
                        // Icon and Instructions
                        VStack(spacing: 16) {
                            Image(systemName: "qrcode.viewfinder")
                                .font(.system(size: 80))
                                .foregroundColor(.blue)

                            Text("Publisher Authentication")
                                .font(.title2)
                                .fontWeight(.semibold)

                            Text("Enter the QR code content from the publisher to authenticate and access exclusive content")
                                .font(.body)
                                .foregroundColor(.secondary)
                                .multilineTextAlignment(.center)
                                .padding(.horizontal, 32)
                        }

                        // Manual Entry Button
                        Button(action: {
                            showingManualEntry = true
                        }) {
                            HStack {
                                Image(systemName: "keyboard")
                                Text("Enter QR Code")
                            }
                            .font(.headline)
                            .foregroundColor(.white)
                            .frame(maxWidth: .infinity)
                            .padding()
                            .background(Color.blue)
                            .cornerRadius(12)
                        }
                        .padding(.horizontal, 32)

                        // Demo Button
                        Button(action: {
                            Task {
                                await viewModel.authenticatePublisher(qrCodeContent: "demo_qr_content")
                            }
                        }) {
                            HStack {
                                Image(systemName: "play.circle")
                                Text("Demo Authentication")
                            }
                            .font(.headline)
                            .foregroundColor(.white)
                            .frame(maxWidth: .infinity)
                            .padding()
                            .background(Color.green)
                            .cornerRadius(12)
                        }
                        .padding(.horizontal, 32)
                    }
                }

                Spacer()
            }
            .navigationTitle("Publisher Auth")
        }
        .sheet(isPresented: $showingManualEntry) {
            ManualQREntryView { result in
                showingManualEntry = false
                Task {
                    await viewModel.authenticatePublisher(qrCodeContent: result)
                }
            }
        }
        .alert("Error", isPresented: .constant(viewModel.errorMessage != nil)) {
            Button("OK") {
                viewModel.clearError()
            }
        } message: {
            if let error = viewModel.errorMessage {
                Text(error)
            }
        }
    }
}

struct AuthSuccessView: View {
    let authResult: OnboardUserResponse
    let onDismiss: () -> Void
    
    var body: some View {
        VStack(spacing: 24) {
            Image(systemName: "checkmark.circle.fill")
                .font(.system(size: 80))
                .foregroundColor(.green)
            
            Text("Authentication Successful!")
                .font(.title2)
                .fontWeight(.bold)
            
            VStack(spacing: 8) {
                Text("Welcome, \(authResult.name)")
                    .font(.headline)
                
                Text(authResult.email)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
            
            Button("Continue") {
                onDismiss()
            }
            .font(.headline)
            .foregroundColor(.white)
            .frame(maxWidth: .infinity)
            .padding()
            .background(Color.green)
            .cornerRadius(12)
            .padding(.horizontal, 32)
        }
        .padding(32)
        .background(Color(NSColor.controlBackgroundColor))
        .cornerRadius(16)
        .shadow(color: .black.opacity(0.1), radius: 10, x: 0, y: 5)
        .padding(.horizontal, 24)
    }
}

struct ManualQREntryView: View {
    let onCodeEntered: (String) -> Void
    @State private var qrCodeText = ""
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            VStack(spacing: 24) {
                Text("Enter QR Code Content")
                    .font(.title2)
                    .fontWeight(.semibold)

                Text("Paste or type the QR code content from the publisher")
                    .font(.body)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)

                TextField("QR Code Content", text: $qrCodeText)
                    .textFieldStyle(.roundedBorder)
                    .padding(.horizontal)

                HStack(spacing: 16) {
                    Button("Cancel") {
                        dismiss()
                    }
                    .buttonStyle(.bordered)

                    Button("Authenticate") {
                        onCodeEntered(qrCodeText)
                    }
                    .buttonStyle(.borderedProminent)
                    .disabled(qrCodeText.isEmpty)
                }

                Spacer()
            }
            .padding()
            .navigationTitle("Manual Entry")
        }
    }
}

#Preview {
    PublisherAuthView()
}
