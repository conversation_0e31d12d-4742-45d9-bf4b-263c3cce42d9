import SwiftUI
import MonetaSDK

struct PublisherAuthView: View {
    @StateObject private var viewModel = PublisherAuthViewModel()
    @State private var showingQRScanner = false
    @State private var showingManualEntry = false

    var body: some View {
        NavigationView {
            VStack(spacing: 24) {
                Spacer()

                if viewModel.isLoading {
                    ProgressView("Authenticating...")
                        .scaleEffect(1.2)
                } else if let authResult = viewModel.authResult {
                    AuthSuccessView(authResult: authResult) {
                        viewModel.clearAuthResult()
                    }
                } else {
                    VStack(spacing: 32) {
                        // Icon and Instructions
                        VStack(spacing: 16) {
                            Image(systemName: "qrcode.viewfinder")
                                .font(.system(size: 80))
                                .foregroundColor(.blue)

                            Text("Publisher Authentication")
                                .font(.title2)
                                .fontWeight(.semibold)

                            Text(instructionText)
                                .font(.body)
                                .foregroundColor(.secondary)
                                .multilineTextAlignment(.center)
                                .padding(.horizontal, 32)
                        }

                        VStack(spacing: 16) {
                            // QR Scanner Button (iOS only)
                            if PlatformService.shared.canScanQRCode() {
                                Button(action: {
                                    showingQRScanner = true
                                }) {
                                    HStack {
                                        Image(systemName: "camera")
                                        Text("Scan QR Code")
                                    }
                                    .font(.headline)
                                    .foregroundColor(.white)
                                    .frame(maxWidth: .infinity)
                                    .padding()
                                    .background(Color.blue)
                                    .cornerRadius(12)
                                }
                                .padding(.horizontal, 32)
                            }

                            // Manual Entry Button
                            Button(action: {
                                showingManualEntry = true
                            }) {
                                HStack {
                                    Image(systemName: "keyboard")
                                    Text("Enter QR Code")
                                }
                                .font(.headline)
                                .foregroundColor(.white)
                                .frame(maxWidth: .infinity)
                                .padding()
                                .background(Color.orange)
                                .cornerRadius(12)
                            }
                            .padding(.horizontal, 32)

                            // Demo Button
                            Button(action: {
                                Task {
                                    await viewModel.authenticatePublisher(qrCodeContent: "demo_qr_content")
                                }
                            }) {
                                HStack {
                                    Image(systemName: "play.circle")
                                    Text("Demo Authentication")
                                }
                                .font(.headline)
                                .foregroundColor(.white)
                                .frame(maxWidth: .infinity)
                                .padding()
                                .background(Color.green)
                                .cornerRadius(12)
                            }
                            .padding(.horizontal, 32)
                        }
                    }
                }

                Spacer()
            }
            .navigationTitle("Publisher Auth")
        }
        .qrScannerSheet(isPresented: $showingQRScanner) { result in
            showingQRScanner = false
            Task {
                await viewModel.authenticatePublisher(qrCodeContent: result)
            }
        }
        .sheet(isPresented: $showingManualEntry) {
            ManualQREntryView { result in
                showingManualEntry = false
                Task {
                    await viewModel.authenticatePublisher(qrCodeContent: result)
                }
            }
        }
        .alert("Error", isPresented: .constant(viewModel.errorMessage != nil)) {
            Button("OK") {
                viewModel.clearError()
            }
        } message: {
            if let error = viewModel.errorMessage {
                Text(error)
            }
        }
    }

    private var instructionText: String {
        if PlatformService.shared.canScanQRCode() {
            return "Scan the QR code from the publisher or enter it manually to authenticate and access exclusive content"
        } else {
            return "Enter the QR code content from the publisher to authenticate and access exclusive content"
        }
    }
}

struct AuthSuccessView: View {
    let authResult: OnboardUserResponse
    let onDismiss: () -> Void
    
    var body: some View {
        VStack(spacing: 24) {
            Image(systemName: "checkmark.circle.fill")
                .font(.system(size: 80))
                .foregroundColor(.green)
            
            Text("Authentication Successful!")
                .font(.title2)
                .fontWeight(.bold)
            
            VStack(spacing: 8) {
                Text("Welcome, \(authResult.name)")
                    .font(.headline)
                
                Text(authResult.email)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
            
            Button("Continue") {
                onDismiss()
            }
            .font(.headline)
            .foregroundColor(.white)
            .frame(maxWidth: .infinity)
            .padding()
            .background(Color.green)
            .cornerRadius(12)
            .padding(.horizontal, 32)
        }
        .padding(32)
        .platformBackground()
        .cornerRadius(16)
        .shadow(color: .black.opacity(0.1), radius: 10, x: 0, y: 5)
        .padding(.horizontal, 24)
    }
}

// MARK: - Platform-specific QR Scanner Sheet Extension
extension View {
    func qrScannerSheet(isPresented: Binding<Bool>, onCodeScanned: @escaping (String) -> Void) -> some View {
        #if os(iOS)
        return self.sheet(isPresented: isPresented) {
            QRCodeScannerView(onCodeScanned: onCodeScanned)
        }
        #else
        return self
        #endif
    }
}

#Preview {
    PublisherAuthView()
}
