import SwiftUI
import Moneta<PERSON><PERSON>

struct TransactionRow: View {
    let transaction: TransactionResponse
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(transaction.publisherName)
                        .font(.headline)
                        .lineLimit(1)
                    
                    Text(formatDate(transaction.createdAt))
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                VStack(alignment: .trailing, spacing: 4) {
                    Text(formatCurrency(transaction.amount, currency: transaction.currencyCode))
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundColor(transaction.amount >= 0 ? .green : .red)
                    
                    StatusBadge(status: transaction.status)
                }
            }
            
            if !transaction.description.isEmpty {
                Text(transaction.description)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .lineLimit(2)
            }
        }
        .padding(16)
        .platformBackground()
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.05), radius: 2, x: 0, y: 1)
    }
    
    private func formatCurrency(_ amount: Double, currency: String) -> String {
        let formatter = NumberFormatter()
        formatter.numberStyle = .currency
        formatter.currencyCode = currency
        return formatter.string(from: NSNumber(value: amount)) ?? "$\(amount)"
    }
    
    private func formatDate(_ dateString: String) -> String {
        let inputFormatter = DateFormatter()
        inputFormatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss.SSSSSS'Z'"
        inputFormatter.timeZone = TimeZone(abbreviation: "UTC")
        
        let outputFormatter = DateFormatter()
        outputFormatter.dateStyle = .medium
        outputFormatter.timeStyle = .short
        
        if let date = inputFormatter.date(from: dateString) {
            return outputFormatter.string(from: date)
        } else {
            return dateString
        }
    }
}

struct StatusBadge: View {
    let status: String
    
    var body: some View {
        Text(status.capitalized)
            .font(.caption2)
            .fontWeight(.medium)
            .padding(.horizontal, 8)
            .padding(.vertical, 4)
            .background(backgroundColor)
            .foregroundColor(textColor)
            .cornerRadius(8)
    }
    
    private var backgroundColor: Color {
        switch status.lowercased() {
        case "completed", "success":
            return .green.opacity(0.2)
        case "pending":
            return .orange.opacity(0.2)
        case "failed", "error":
            return .red.opacity(0.2)
        default:
            return .gray.opacity(0.2)
        }
    }
    
    private var textColor: Color {
        switch status.lowercased() {
        case "completed", "success":
            return .green
        case "pending":
            return .orange
        case "failed", "error":
            return .red
        default:
            return .gray
        }
    }
}

#Preview {
    VStack {
        TransactionRow(transaction: TransactionResponse(
            id: "1",
            type: "purchase",
            userId: "user_123",
            publisherId: "pub_123",
            publisherName: "Starbucks",
            amount: 25.99,
            interchangeFee: 0.75,
            status: "completed",
            currencyCode: "USD",
            description: "Coffee purchase at Starbucks",
            createdAt: "2024-01-15T10:30:00.000000Z",
            updatedAt: "2024-01-15T10:30:00.000000Z"
        ))

        TransactionRow(transaction: TransactionResponse(
            id: "2",
            type: "refund",
            userId: "user_123",
            publisherId: "pub_456",
            publisherName: "Amazon",
            amount: -15.50,
            interchangeFee: 0.0,
            status: "pending",
            currencyCode: "USD",
            description: "Refund for cancelled order",
            createdAt: "2024-01-14T14:20:00.000000Z",
            updatedAt: "2024-01-14T14:20:00.000000Z"
        ))
    }
    .padding()
}
