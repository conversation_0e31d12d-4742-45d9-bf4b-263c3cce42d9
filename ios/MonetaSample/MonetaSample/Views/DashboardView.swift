import SwiftUI
import MonetaSDK

struct DashboardView: View {
    @StateObject private var viewModel = DashboardViewModel()
    
    var body: some View {
        NavigationView {
            ScrollView {
                LazyVStack(spacing: 20) {
                    if viewModel.isLoading {
                        ProgressView("Loading...")
                            .frame(maxWidth: .infinity, maxHeight: .infinity)
                            .padding(.top, 100)
                    } else {
                        // Balance Card
                        BalanceCard(balance: viewModel.balance)
                        
                        // Recent Transactions Section
                        VStack(alignment: .leading, spacing: 16) {
                            HStack {
                                Text("Recent Transactions")
                                    .font(.title2)
                                    .fontWeight(.semibold)
                                Spacer()
                            }
                            
                            if viewModel.recentTransactions.isEmpty {
                                EmptyTransactionsCard()
                            } else {
                                ForEach(viewModel.recentTransactions, id: \.id) { transaction in
                                    TransactionRow(transaction: transaction)
                                }
                                
                                Button("View All") {
                                    // Navigate to transactions tab
                                }
                                .font(.subheadline)
                                .foregroundColor(.blue)
                                .frame(maxWidth: .infinity)
                                .padding(.vertical, 8)
                            }
                        }
                    }
                }
                .padding(.horizontal, 16)
                .padding(.top, 8)
            }
            .navigationTitle("Dashboard")
            .refreshable {
                await viewModel.loadDashboardData()
            }
        }
        .alert("Error", isPresented: .constant(viewModel.errorMessage != nil)) {
            Button("Retry") {
                Task {
                    await viewModel.loadDashboardData()
                }
            }
            Button("OK") {
                viewModel.clearError()
            }
        } message: {
            if let error = viewModel.errorMessage {
                Text(error)
            }
        }
        .task {
            await viewModel.loadDashboardData()
        }
    }
}

struct BalanceCard: View {
    let balance: UserBalanceResponse?
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Current Balance")
                .font(.headline)
                .foregroundColor(.white)
            
            if let balance = balance {
                Text(formatCurrency(balance.balance, currency: balance.currency))
                    .font(.largeTitle)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
                
                if let pending = balance.pendingAmount, pending > 0 {
                    Text("Pending: \(formatCurrency(pending, currency: balance.currency))")
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.8))
                }
            } else {
                Text("--")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
            }
        }
        .frame(maxWidth: .infinity, alignment: .leading)
        .padding(20)
        .background(
            LinearGradient(
                colors: [.blue, .blue.opacity(0.8)],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        )
        .cornerRadius(16)
    }
    
    private func formatCurrency(_ amount: Double, currency: String) -> String {
        let formatter = NumberFormatter()
        formatter.numberStyle = .currency
        formatter.currencyCode = currency
        return formatter.string(from: NSNumber(value: amount)) ?? "$\(amount)"
    }
}

struct EmptyTransactionsCard: View {
    var body: some View {
        VStack(spacing: 12) {
            Image(systemName: "creditcard")
                .font(.system(size: 40))
                .foregroundColor(.gray)
            
            Text("No transactions found")
                .font(.body)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity)
        .padding(32)
        .platformSecondaryBackground()
        .cornerRadius(12)
    }
}

#Preview {
    DashboardView()
}
