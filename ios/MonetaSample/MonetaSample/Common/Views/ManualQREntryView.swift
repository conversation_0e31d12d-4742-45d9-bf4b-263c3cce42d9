import SwiftUI

struct ManualQREntryView: View {
    let onCodeEntered: (String) -> Void
    @State private var qrCodeText = ""
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack(spacing: 24) {
                Text("Enter QR Code Content")
                    .font(.title2)
                    .fontWeight(.semibold)
                
                Text("Paste or type the QR code content from the publisher")
                    .font(.body)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                
                TextField("QR Code Content", text: $qrCodeText)
                    .textFieldStyle(.roundedBorder)
                    .padding(.horizontal)
                
                HStack(spacing: 16) {
                    But<PERSON>("Cancel") {
                        dismiss()
                    }
                    .buttonStyle(.bordered)
                    
                    Button("Authenticate") {
                        onCodeEntered(qrCodeText)
                    }
                    .buttonStyle(.borderedProminent)
                    .disabled(qrCodeText.isEmpty)
                }
                
                Spacer()
            }
            .padding()
            .navigationTitle("Manual Entry")
            .platformNavigationTitleDisplayMode()
        }
    }
}
