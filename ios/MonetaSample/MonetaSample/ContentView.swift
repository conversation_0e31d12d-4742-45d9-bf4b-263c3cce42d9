import SwiftUI

struct ContentView: View {
    @EnvironmentObject var appState: AppState

    var body: some View {
        Group {
            if !appState.isSDKInitialized {
                SDKInitializationView()
            } else if appState.isOnboardingCompleted {
                MainTabView()
            } else {
                OnboardingView()
            }
        }
        .onAppear {
            appState.checkOnboardingStatus()
        }
    }
}

// MARK: - SDK Initialization View
struct SDKInitializationView: View {
    @EnvironmentObject var appState: AppState

    var body: some View {
        VStack(spacing: 20) {
            if let error = appState.initializationError {
                Image(systemName: "exclamationmark.triangle")
                    .font(.system(size: 50))
                    .foregroundColor(.red)

                Text("SDK Initialization Failed")
                    .font(.title2)
                    .fontWeight(.semibold)

                Text(error.localizedDescription)
                    .font(.body)
                    .multilineTextAlignment(.center)
                    .foregroundColor(.secondary)
                    .padding(.horizontal)

                But<PERSON>("Retry") {
                    // In a real app, you might want to retry initialization
                    // For now, we'll just show this button
                }
                .buttonStyle(.borderedProminent)
            } else {
                ProgressView()
                    .scaleEffect(1.5)

                Text("Initializing Moneta SDK...")
                    .font(.title2)
                    .fontWeight(.medium)

                Text("Please wait while we set up the SDK")
                    .font(.body)
                    .foregroundColor(.secondary)
            }
        }
        .padding()
    }
}

// MARK: - App State Management
@MainActor
class AppState: ObservableObject {
    @Published var isSDKInitialized = false
    @Published var initializationError: Error?
    @Published var isOnboardingCompleted: Bool = false
    @Published var isLoading: Bool = true

    func checkOnboardingStatus() {
        // In a real app, you would check if the user has completed onboarding
        // For demo purposes, we'll assume they need to onboard first
        Task {
            try? await Task.sleep(nanoseconds: 1_000_000_000) // 1 second delay
            isLoading = false
            // Check if user data exists in secure storage
            // For now, we'll default to false to show onboarding
            isOnboardingCompleted = false
        }
    }

    func completeOnboarding() {
        isOnboardingCompleted = true
    }
}

#Preview {
    ContentView()
}
