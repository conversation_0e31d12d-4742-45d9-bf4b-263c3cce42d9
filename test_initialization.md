# Testing the Revised Initialization Workflow

## Test Scenarios

### 1. Valid URL with Successful fetchMoInfo

**Expected Behavior:**
- URL validation passes
- Network clients are initialized
- fetchMoInfo is called automatically
- MO info data (currency, region) is saved to storage
- SDK is marked as fully initialized

### 2. Invalid URL Format

**Test Cases:**
- `"invalid-url"` - No protocol
- `"ftp://example.com"` - Invalid protocol
- `"https://"` - Missing host
- `""` - Empty string

**Expected Behavior:**
- URL validation fails immediately
- Initialization throws error
- SDK remains uninitialized

### 3. Valid URL but fetchMoInfo Fails

**Expected Behavior:**
- URL validation passes
- Network clients are initialized
- fetchMoInfo is called but fails (network error, API error, etc.)
- Partial initialization is cleaned up
- SDK remains uninitialized
- Error is thrown to caller

### 4. Accessing MO Info After Successful Initialization

**Expected Behavior:**
- `getMoInfoCurrency()` returns stored currency code
- `getMoInfoRegion()` returns stored region
- `isSDKFullyInitialized()` returns true

## Manual Testing Steps

### iOS Testing

```swift
import MonetaSDK

// Test 1: Valid initialization
func testValidInitialization() async {
    do {
        try await MonetaSDK.shared.initialize(baseUrl: "https://membership-portal.dev.pressingly.net")
        
        print("✅ Initialization successful")
        print("Is fully initialized: \(MonetaSDK.shared.isSDKFullyInitialized())")
        
        if let currency = try MonetaSDK.shared.getMoInfoCurrency() {
            print("Currency: \(currency)")
        }
        
        if let region = try MonetaSDK.shared.getMoInfoRegion() {
            print("Region: \(region)")
        }
        
    } catch {
        print("❌ Initialization failed: \(error)")
    }
}

// Test 2: Invalid URL
func testInvalidURL() async {
    do {
        try await MonetaSDK.shared.initialize(baseUrl: "invalid-url")
        print("❌ Should have failed with invalid URL")
    } catch {
        print("✅ Correctly failed with invalid URL: \(error)")
    }
}
```

### Android Testing

```kotlin
import com.moneta.sdk.MonetaSDK
import kotlinx.coroutines.runBlocking

// Test 1: Valid initialization
fun testValidInitialization() {
    runBlocking {
        try {
            MonetaSDK.initialize(context, "https://membership-portal.dev.pressingly.net")
            
            println("✅ Initialization successful")
            println("Is fully initialized: ${MonetaSDK.instance.isSDKFullyInitialized()}")
            
            val currency = MonetaSDK.instance.getMoInfoCurrency()
            val region = MonetaSDK.instance.getMoInfoRegion()
            
            println("Currency: $currency")
            println("Region: $region")
            
        } catch (e: Exception) {
            println("❌ Initialization failed: ${e.message}")
        }
    }
}

// Test 2: Invalid URL
fun testInvalidURL() {
    runBlocking {
        try {
            MonetaSDK.initialize(context, "invalid-url")
            println("❌ Should have failed with invalid URL")
        } catch (e: Exception) {
            println("✅ Correctly failed with invalid URL: ${e.message}")
        }
    }
}
```

## Expected Test Results

### Successful Initialization
```
✅ Initialization successful
Is fully initialized: true
Currency: USD
Region: US
```

### Invalid URL Test
```
✅ Correctly failed with invalid URL: Invalid base URL format. Must be a valid HTTP or HTTPS URL.
```

### Network Failure Test
```
❌ Initialization failed: Failed to fetch MO info
```

## Verification Checklist

- [ ] URL validation works for various invalid formats
- [ ] Valid URLs pass validation
- [ ] fetchMoInfo is called automatically during initialization
- [ ] MO info data is saved to secure storage
- [ ] SDK is marked as fully initialized only after successful fetchMoInfo
- [ ] Partial initialization is cleaned up on failure
- [ ] getMoInfoCurrency() returns correct value
- [ ] getMoInfoRegion() returns correct value
- [ ] isSDKFullyInitialized() returns correct status
- [ ] Error handling works correctly for all failure scenarios

## Notes

- Use the development API endpoint: `https://membership-portal.dev.pressingly.net`
- Expected MO info response: `{"currencyCode": "USD", "region": "US"}`
- Test both platforms to ensure consistent behavior
- Verify that storage keys are correctly used across both platforms
