# SDK Initialization Workflow Revision

## Overview

The SDK initialization workflow has been revised to ensure that the SDK is only considered fully initialized when:
1. The baseURL is valid
2. The `fetchMoInfo` API call is successful
3. The returned MO info data (currency and region) is saved to storage

## Changes Made

### 1. New MO Info Response Model

**iOS (`MoInfoResponse`):**
```swift
public struct MoInfoResponse: Codable {
    public let currencyCode: String
    public let region: String
}
```

**Android (`MoInfoResponse`):**
```kotlin
@Serializable
data class MoInfoResponse(
    @SerialName("currencyCode") val currencyCode: String,
    @SerialName("region") val region: String
)
```

### 2. Enhanced Initialization State Management

**New Properties Added:**
- `isFullyInitialized: Bool/Boolean` - Tracks complete initialization status
- Storage keys for MO info data and initialization state

**Storage Keys:**
- `mo_info_currency` - Stores currency code from fetchMoInfo
- `mo_info_region` - Stores region from fetchMoInfo  
- `sdk_initialization_state` - Tracks initialization completion

### 3. URL Validation

**iOS:**
```swift
private func validateBaseUrl(_ url: String) throws {
    guard let urlComponents = URLComponents(string: url),
          let scheme = urlComponents.scheme,
          scheme.lowercased() == "http" || scheme.lowercased() == "https",
          urlComponents.host != nil else {
        throw MonetaSDKError.networkError("Invalid base URL format. Must be a valid HTTP or HTTPS URL.")
    }
}
```

**Android:**
```kotlin
private fun validateBaseUrl(url: String) {
    try {
        val uri = java.net.URI(url)
        if (uri.scheme == null || (!uri.scheme.equals("http", ignoreCase = true) && !uri.scheme.equals("https", ignoreCase = true))) {
            throw MonetaException.ApiException(400, "Invalid base URL format. Must be a valid HTTP or HTTPS URL.")
        }
        if (uri.host == null) {
            throw MonetaException.ApiException(400, "Invalid base URL format. Must include a valid host.")
        }
    } catch (e: Exception) {
        throw MonetaException.ApiException(400, "Invalid base URL format: ${e.message}")
    }
}
```

### 4. Automatic MO Info Fetching

**iOS:**
```swift
private func performInitialMoInfoFetch() async throws {
    // Fetch MO info
    let response = try await membershipOrgClient.fetchMoInfo()
    
    if response.success, let data = response.data {
        // Parse and save MO info
        let jsonData = try JSONSerialization.data(withJSONObject: data.value)
        let moInfo = try JSONDecoder().decode(MoInfoResponse.self, from: jsonData)
        
        try secureStorage.saveData(moInfoCurrencyKey, value: moInfo.currencyCode)
        try secureStorage.saveData(moInfoRegionKey, value: moInfo.region)
        try secureStorage.saveData(initializationStateKey, value: "completed")
        
        self.isFullyInitialized = true
    } else {
        throw MonetaSDKError.apiError(response.status, response.message ?? "Failed to fetch MO info")
    }
}
```

**Android:**
```kotlin
private suspend fun performInitialMoInfoFetch() {
    val response = membershipOrgClient!!.fetchMoInfo()
    if (response.success && response.data != null) {
        // Parse and save MO info
        val moInfo = jsonSerializer!!.decodeFromJsonElement<MoInfoResponse>(response.data)
        
        secureStorage!!.saveData(moInfoCurrencyKey, moInfo.currencyCode)
        secureStorage!!.saveData(moInfoRegionKey, moInfo.region)
        secureStorage!!.saveData(initializationStateKey, "completed")
        
        isFullyInitialized = true
    } else {
        throw MonetaException.ApiException(response.status, response.message ?: "Failed to fetch MO info")
    }
}
```

### 5. Updated Initialization Methods

**iOS:**
```swift
public func initialize(baseUrl: String) async throws {
    // Validate URL, setup clients, then fetch MO info
    try validateBaseUrl(baseUrl)
    // ... setup code ...
    try await performInitialMoInfoFetch()
}
```

**Android:**
```kotlin
suspend fun initialize(context: Context, baseUrl: String) {
    // Validate URL, setup clients, then fetch MO info
    validateBaseUrl(baseUrl)
    // ... setup code ...
    performInitialMoInfoFetch()
}
```

### 6. Enhanced Initialization Checking

**Updated `checkInitialized()` methods:**
- Now also verifies `isFullyInitialized` flag
- Ensures MO info has been successfully fetched and stored

### 7. New Public API Methods

**MO Info Access Methods:**
- `getMoInfoCurrency()` - Returns stored currency code
- `getMoInfoRegion()` - Returns stored region
- `isSDKFullyInitialized()` - Returns initialization status

### 8. Error Handling and Cleanup

**Failure Recovery:**
- If `fetchMoInfo` fails, the SDK cleans up partial initialization
- Resets all internal state to prevent inconsistent state
- Throws appropriate error to caller

## Usage Examples

### iOS
```swift
do {
    try await MonetaSDK.shared.initialize(baseUrl: "https://api.moneta.com")
    
    // SDK is now fully initialized
    let currency = try MonetaSDK.shared.getMoInfoCurrency()
    let region = try MonetaSDK.shared.getMoInfoRegion()
    
    print("Initialized with currency: \(currency), region: \(region)")
} catch {
    print("Initialization failed: \(error)")
}
```

### Android
```kotlin
try {
    MonetaSDK.initialize(context, "https://api.moneta.com")
    
    // SDK is now fully initialized
    val currency = MonetaSDK.instance.getMoInfoCurrency()
    val region = MonetaSDK.instance.getMoInfoRegion()
    
    println("Initialized with currency: $currency, region: $region")
} catch (e: Exception) {
    println("Initialization failed: ${e.message}")
}
```

## Benefits

1. **Guaranteed Valid State**: SDK is only marked as initialized after successful MO info fetch
2. **Data Persistence**: Currency and region are automatically stored for offline access
3. **URL Validation**: Prevents initialization with invalid URLs
4. **Error Recovery**: Clean failure handling with state cleanup
5. **Consistent API**: Both platforms follow the same initialization pattern
6. **Backward Compatibility**: Existing error handling patterns maintained

## Breaking Changes

1. **Async Initialization**: Both platforms now require async/await or suspend functions
2. **Enhanced Error Handling**: More specific error types for initialization failures
3. **Stricter Validation**: Invalid URLs will now cause initialization to fail

This revision ensures that the SDK provides a more robust and reliable initialization experience while maintaining the existing API patterns and error handling approaches.
