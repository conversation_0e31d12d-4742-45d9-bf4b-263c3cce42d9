package com.moneta.sampleapp.ui.navigation

import androidx.compose.foundation.layout.padding
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Dashboard
import androidx.compose.material.icons.filled.List
import androidx.compose.material.icons.filled.QrCode
import androidx.compose.material.icons.filled.Star
import androidx.compose.material.icons.filled.Settings
import androidx.compose.material3.Icon
import androidx.compose.material3.NavigationBar
import androidx.compose.material3.NavigationBarItem
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.stringResource
import androidx.navigation.NavDestination.Companion.hierarchy
import androidx.navigation.NavGraph.Companion.findStartDestination
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.currentBackStackEntryAsState
import androidx.navigation.compose.rememberNavController
import com.moneta.sampleapp.R
import com.moneta.sampleapp.ui.screens.dashboard.DashboardScreen
import com.moneta.sampleapp.ui.screens.initialization.SDKInitializationScreen
import com.moneta.sampleapp.ui.screens.onboarding.OnboardingScreen
import com.moneta.sampleapp.ui.screens.publisherauth.PublisherAuthScreen
import com.moneta.sampleapp.ui.screens.recommendations.RecommendationsScreen
import com.moneta.sampleapp.ui.screens.settings.SettingsScreen
import com.moneta.sampleapp.ui.screens.transactions.TransactionsScreen

sealed class Screen(val route: String, val titleRes: Int, val icon: ImageVector) {
    object SDKInitialization : Screen("sdk_initialization", R.string.onboarding_title, Icons.Default.Dashboard)
    object Onboarding : Screen("onboarding", R.string.onboarding_title, Icons.Default.Dashboard)
    object Dashboard : Screen("dashboard", R.string.nav_dashboard, Icons.Default.Dashboard)
    object Transactions : Screen("transactions", R.string.nav_transactions, Icons.Default.List)
    object Recommendations : Screen("recommendations", R.string.nav_recommendations, Icons.Default.Star)
    object PublisherAuth : Screen("publisher_auth", R.string.nav_publisher_auth, Icons.Default.QrCode)
    object Settings : Screen("settings", R.string.nav_settings, Icons.Default.Settings)
}

val bottomNavItems = listOf(
    Screen.Dashboard,
    Screen.Transactions,
    Screen.Recommendations,
    Screen.PublisherAuth,
    Screen.Settings
)

@Composable
fun MonetaNavigation() {
    val navController = rememberNavController()
    
    Scaffold(
        bottomBar = {
            val navBackStackEntry by navController.currentBackStackEntryAsState()
            val currentDestination = navBackStackEntry?.destination
            
            // Only show bottom navigation if not on initialization or onboarding screen
            if (currentDestination?.route != Screen.SDKInitialization.route &&
                currentDestination?.route != Screen.Onboarding.route) {
                NavigationBar {
                    bottomNavItems.forEach { screen ->
                        NavigationBarItem(
                            icon = { Icon(screen.icon, contentDescription = null) },
                            label = { Text(stringResource(screen.titleRes)) },
                            selected = currentDestination?.hierarchy?.any { it.route == screen.route } == true,
                            onClick = {
                                navController.navigate(screen.route) {
                                    // Pop up to the start destination of the graph to
                                    // avoid building up a large stack of destinations
                                    // on the back stack as users select items
                                    popUpTo(navController.graph.findStartDestination().id) {
                                        saveState = true
                                    }
                                    // Avoid multiple copies of the same destination when
                                    // reselecting the same item
                                    launchSingleTop = true
                                    // Restore state when reselecting a previously selected item
                                    restoreState = true
                                }
                            }
                        )
                    }
                }
            }
        }
    ) { innerPadding ->
        NavHost(
            navController = navController,
            startDestination = Screen.SDKInitialization.route,
            modifier = Modifier.padding(innerPadding)
        ) {
            composable(Screen.SDKInitialization.route) {
                SDKInitializationScreen(
                    onInitializationComplete = {
                        navController.navigate(Screen.Onboarding.route) {
                            popUpTo(Screen.SDKInitialization.route) { inclusive = true }
                        }
                    }
                )
            }
            composable(Screen.Onboarding.route) {
                OnboardingScreen(
                    onOnboardingComplete = {
                        navController.navigate(Screen.Dashboard.route) {
                            popUpTo(Screen.Onboarding.route) { inclusive = true }
                        }
                    }
                )
            }
            composable(Screen.Dashboard.route) {
                DashboardScreen()
            }
            composable(Screen.Transactions.route) {
                TransactionsScreen()
            }
            composable(Screen.Recommendations.route) {
                RecommendationsScreen()
            }
            composable(Screen.PublisherAuth.route) {
                PublisherAuthScreen()
            }
            composable(Screen.Settings.route) {
                SettingsScreen()
            }
        }
    }
}
