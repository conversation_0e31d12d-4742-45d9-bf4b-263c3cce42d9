package com.moneta.sampleapp.ui.screens.initialization

import android.content.Context
import android.util.Log
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.moneta.sdk.MonetaSDK
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class SDKInitializationViewModel @Inject constructor() : ViewModel() {
    
    private val _uiState = MutableStateFlow(SDKInitializationUiState())
    val uiState: StateFlow<SDKInitializationUiState> = _uiState.asStateFlow()
    
    fun checkInitializationStatus() {
        viewModelScope.launch {
            try {
                // Check if SDK is already initialized
                val isInitialized = MonetaSDK.instance.isSDKFullyInitialized()
                
                if (isInitialized) {
                    // SDK is already initialized, get MO info
                    val currency = MonetaSDK.instance.getMoInfoCurrency()
                    val region = MonetaSDK.instance.getMoInfoRegion()
                    
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        isInitialized = true,
                        moInfo = MoInfo(currency, region)
                    )
                    
                    Log.d("SDKInitialization", "SDK already initialized - Currency: $currency, Region: $region")
                    
                    // Add a small delay to show the success state
                    delay(2000)
                } else {
                    // SDK is not initialized, wait for it to complete
                    Log.d("SDKInitialization", "SDK not yet initialized, waiting...")
                    waitForInitialization()
                }
            } catch (e: Exception) {
                Log.e("SDKInitialization", "Error checking initialization status", e)
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = "Failed to check SDK status: ${e.message}"
                )
            }
        }
    }
    
    private suspend fun waitForInitialization() {
        var attempts = 0
        val maxAttempts = 30 // 30 seconds timeout
        
        while (attempts < maxAttempts) {
            try {
                val isInitialized = MonetaSDK.instance.isSDKFullyInitialized()
                
                if (isInitialized) {
                    // SDK is now initialized
                    val currency = MonetaSDK.instance.getMoInfoCurrency()
                    val region = MonetaSDK.instance.getMoInfoRegion()
                    
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        isInitialized = true,
                        moInfo = MoInfo(currency, region)
                    )
                    
                    Log.d("SDKInitialization", "SDK initialization completed - Currency: $currency, Region: $region")
                    
                    // Add a small delay to show the success state
                    delay(2000)
                    return
                }
                
                // Wait 1 second before checking again
                delay(1000)
                attempts++
                
            } catch (e: Exception) {
                Log.e("SDKInitialization", "Error waiting for initialization", e)
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = "SDK initialization failed: ${e.message}"
                )
                return
            }
        }
        
        // Timeout reached
        _uiState.value = _uiState.value.copy(
            isLoading = false,
            error = "SDK initialization timed out. Please check your network connection and try again."
        )
    }
    
    fun retryInitialization(context: Context) {
        viewModelScope.launch {
            _uiState.value = SDKInitializationUiState(isLoading = true)
            
            try {
                Log.d("SDKInitialization", "Retrying SDK initialization...")
                
                // Retry initialization
                val baseUrl = "https://membership-portal.dev.pressingly.net"
                MonetaSDK.initialize(context, baseUrl)
                
                // Wait for initialization to complete
                waitForInitialization()
                
            } catch (e: Exception) {
                Log.e("SDKInitialization", "Retry initialization failed", e)
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = "Retry failed: ${e.message}"
                )
            }
        }
    }
}
