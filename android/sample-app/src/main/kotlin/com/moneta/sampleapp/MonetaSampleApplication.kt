package com.moneta.sampleapp

import android.app.Application
import android.util.Log
import com.moneta.sdk.MonetaSDK
import dagger.hilt.android.HiltAndroidApp
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch

@HiltAndroidApp
class MonetaSampleApplication : Application() {

    // Application-level coroutine scope
    private val applicationScope = CoroutineScope(SupervisorJob() + Dispatchers.Main)

    override fun onCreate() {
        super.onCreate()

        // Initialize Moneta SDK asynchronously
        // In a real app, you would get this URL from your configuration
        val baseUrl = "https://membership-portal.dev.pressingly.net" // Replace with your actual API URL

        applicationScope.launch {
            try {
                Log.d("MonetaSample", "Starting SDK initialization...")
                MonetaSDK.initialize(this@MonetaSampleApplication, baseUrl)

                // SDK is now fully initialized
                Log.d("MonetaSample", "SDK initialization successful!")
                Log.d("MonetaSample", "Is fully initialized: ${MonetaSDK.instance.isSDKFullyInitialized()}")

                // Access MO info after successful initialization
                val currency = MonetaSDK.instance.getMoInfoCurrency()
                val region = MonetaSDK.instance.getMoInfoRegion()
                Log.d("MonetaSample", "MO Info - Currency: $currency, Region: $region")

            } catch (e: Exception) {
                Log.e("MonetaSample", "SDK initialization failed: ${e.message}", e)
                // Handle initialization failure - you might want to show an error dialog
                // or retry initialization depending on your app's requirements
            }
        }
    }
}