-- Merging decision tree log ---
manifest
ADDED from /Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/AndroidManifest.xml:2:1-42:12
INJECTED from /Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/AndroidManifest.xml:2:1-42:12
INJECTED from /Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/AndroidManifest.xml:2:1-42:12
INJECTED from /Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/AndroidManifest.xml:2:1-42:12
MERGED from [:sdk] /Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sdk/build/intermediates/merged_manifest/release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.hilt:hilt-navigation-compose:1.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/d4b7e4502f5635926ce7e8d1d6916886/transformed/jetified-hilt-navigation-compose-1.1.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.hilt:hilt-navigation:1.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/0d4e809624b7acebf83edd271805b653/transformed/jetified-hilt-navigation-1.1.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-common:2.7.5] /Users/<USER>/.gradle/caches/8.12/transforms/4045b96045558561e5d7dcb95382d7a1/transformed/navigation-common-2.7.5/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime:2.7.5] /Users/<USER>/.gradle/caches/8.12/transforms/740445472f0c11b739d7ce803f3c916e/transformed/navigation-runtime-2.7.5/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common-ktx:2.7.5] /Users/<USER>/.gradle/caches/8.12/transforms/927143c12aeee4bd80653c837b51b276/transformed/navigation-common-ktx-2.7.5/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.5] /Users/<USER>/.gradle/caches/8.12/transforms/4a9155015e7032ffb69f1da53c0b97c3/transformed/navigation-runtime-ktx-2.7.5/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-compose:2.7.5] /Users/<USER>/.gradle/caches/8.12/transforms/51a817c1f00e445d65a34785a78c1af6/transformed/jetified-navigation-compose-2.7.5/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material3:material3:1.1.2] /Users/<USER>/.gradle/caches/8.12/transforms/9aceb0555898d036bfa3b838a4051259/transformed/jetified-material3-1.1.2/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-compose:1.8.2] /Users/<USER>/.gradle/caches/8.12/transforms/0b66e125b08c35c9aa341a33681ad6c5/transformed/jetified-activity-compose-1.8.2/AndroidManifest.xml:2:1-7:12
MERGED from [com.google.dagger:hilt-android:2.48] /Users/<USER>/.gradle/caches/8.12/transforms/1c2f4ae919bb79ac64c14a079e981e48/transformed/jetified-hilt-android-2.48/AndroidManifest.xml:16:1-19:12
MERGED from [androidx.fragment:fragment:1.5.1] /Users/<USER>/.gradle/caches/8.12/transforms/f0abf1c6f8707d5d70b975665ca0a9e2/transformed/fragment-1.5.1/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] /Users/<USER>/.gradle/caches/8.12/transforms/27be7c78474819404dcfe6814e04e3bd/transformed/jetified-savedstate-ktx-1.2.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/8.12/transforms/5a62d5c3c3cb53892767ab7d579872be/transformed/jetified-savedstate-1.2.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/1fccdfa781da93b153ec2557bb58f606/transformed/loader-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [io.coil-kt:coil-compose:2.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/dc7f8685743c0b1efe60441000c42123/transformed/jetified-coil-compose-2.5.0/AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt:coil-compose-base:2.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/3d6d01e68a9b9e375facfbd562faf302/transformed/jetified-coil-compose-base-2.5.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-core-android:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/4dad4f266d96ad6020cf9740531d3471/transformed/jetified-animation-core-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-core-android:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/f95ef8096eae262e04a55812b6b8c3d0/transformed/jetified-material-icons-core-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-extended-android:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/f48e4d76097b95349d877e87800860a7/transformed/jetified-material-icons-extended-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-ripple-android:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/950bd5e44d0de31df548a560080eb744/transformed/jetified-material-ripple-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/d147fd09273078aadeca31119680a5ee/transformed/jetified-animation-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/517d3d920a97aa59b9adf387945c71b9/transformed/jetified-foundation-layout-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/c6f4eab9bf43f1491374830409ee9760/transformed/jetified-foundation-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/ca1e941d2bd9bcaafc5957031628a536/transformed/jetified-ui-unit-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/0a80abd5c617424f3a501268b55ba7d8/transformed/jetified-ui-geometry-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/7c19b2d242422f17e97f30035e53c3a9/transformed/jetified-ui-util-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/b321e078f9f8c3d35e824ddce3fcf9f6/transformed/jetified-ui-tooling-preview-release/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/e04f21f9f9708bebbfbb6e5f55174392/transformed/jetified-ui-graphics-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/1476e5beafce493c017134248d9b3796/transformed/jetified-ui-text-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/b4374047bc445910aa5fc28ce452a90c/transformed/jetified-emoji2-1.4.0/AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/54692633a1b1700654ac536ed9f93313/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/c403f1a2cc8aaffaa2bf644415e14888/transformed/jetified-lifecycle-livedata-core-ktx-2.7.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/378041e336fdfb1ee8162302a0fad1d3/transformed/lifecycle-livedata-2.7.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/4df479c468ae771903bf93ee550a95de/transformed/lifecycle-livedata-core-2.7.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/259d0b71e1cd1204b212da6f98223a81/transformed/lifecycle-viewmodel-2.7.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/76e32685a77ba7330edacfee9d4734ad/transformed/jetified-lifecycle-viewmodel-savedstate-2.7.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/15863efbebeaf1b382dd6173661e2892/transformed/jetified-lifecycle-viewmodel-ktx-2.7.0/AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt:coil:2.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/9a5d94824a7efe14d375ef0034680f90/transformed/jetified-coil-2.5.0/AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt:coil-base:2.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/bf7ce8bc449644adbbe7701607436e97/transformed/jetified-coil-base-2.5.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/b9659bba57388a7c47d83f18237ab725/transformed/lifecycle-runtime-2.7.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-compose:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/da12a843988567b4171b5ec6f3097b15/transformed/jetified-lifecycle-runtime-compose-2.7.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/fee67b8b02e8a025f663d6f220b695da/transformed/jetified-lifecycle-runtime-ktx-2.7.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/004d42a4e87d4593fffb68471454c2c2/transformed/jetified-lifecycle-viewmodel-compose-2.7.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.google.accompanist:accompanist-drawablepainter:0.32.0] /Users/<USER>/.gradle/caches/8.12/transforms/0dd2fb74628537fe6ea0ea0a76580b0b/transformed/jetified-accompanist-drawablepainter-0.32.0/AndroidManifest.xml:17:1-23:12
MERGED from [androidx.compose.ui:ui-android:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/6dcd5d8b64257e5a867f481fb385b30c/transformed/jetified-ui-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-ktx:1.8.2] /Users/<USER>/.gradle/caches/8.12/transforms/c5c812536c1dc1eac50576521d3a78c0/transformed/jetified-activity-ktx-1.8.2/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.8.2] /Users/<USER>/.gradle/caches/8.12/transforms/e7f374bb96735f0f7f4fe25b91aab5b5/transformed/jetified-activity-1.8.2/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/07dc29200c028a86837fa262ff63b3f9/transformed/viewpager-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.autofill:autofill:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/48c682baeaed42a100799b805e4e6acc/transformed/jetified-autofill-1.0.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/d94e55162f72843d9568bebe6e3aad8e/transformed/customview-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] /Users/<USER>/.gradle/caches/8.12/transforms/c2fd60454ffad06625074a3fcabe3477/transformed/jetified-appcompat-resources-1.6.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/465801801c9464eb74caeab10dcb173e/transformed/vectordrawable-animated-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/a59af7079a95d20f0cffbb5232f06ded/transformed/vectordrawable-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.12/transforms/b52eefb7eefa5c6ad84a8949ac37e1b4/transformed/core-1.12.0/AndroidManifest.xml:17:1-30:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/e2a3a2e00d33eaf6b3bcebf95e42ffaf/transformed/jetified-customview-poolingcontainer-1.0.0/AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.12.0] /Users/<USER>/.gradle/caches/8.12/transforms/7ebdbedc48c0fe6597efbcb4ca42db67/transformed/jetified-core-ktx-1.12.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/eddc93bfd5629ef66100108312dd6ce5/transformed/jetified-runtime-saveable-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/b1b7f44aee1cf79ac965ecc3bcb423e1/transformed/jetified-runtime-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/59e52510e7060bed3731d040990aa23c/transformed/jetified-annotation-experimental-1.3.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/dba0856e066e5109e479c688e8875839/transformed/interpolator-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/d78b48e300ce6c9a51bb25e989698fc0/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:17:1-27:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/a315a6a6a95a04cd876c6fca96b939ca/transformed/core-runtime-2.2.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/3fbcbafc1e60471e08c002f3f605bdd0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/513de38e430506ac1982fabb6728611a/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/506c27cdfd10f86a4453bebc87767a02/transformed/jetified-tracing-1.0.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.exifinterface:exifinterface:1.3.6] /Users/<USER>/.gradle/caches/8.12/transforms/0540caa3e57c85f414a13e37573913ff/transformed/exifinterface-1.3.6/AndroidManifest.xml:17:1-24:12
MERGED from [com.journeyapps:zxing-android-embedded:4.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/93bc7b62ed7d90b46adb14d1a6562db2/transformed/jetified-zxing-android-embedded-4.3.0/AndroidManifest.xml:17:1-56:12
MERGED from [com.google.dagger:dagger-lint-aar:2.48] /Users/<USER>/.gradle/caches/8.12/transforms/b8815893253a0c979a6f901e3355b6e0/transformed/jetified-dagger-lint-aar-2.48/AndroidManifest.xml:16:1-19:12
	package
		INJECTED from /Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/AndroidManifest.xml
	android:versionName
		INJECTED from /Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/AndroidManifest.xml
	xmlns:tools
		ADDED from /Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from /Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/AndroidManifest.xml
	xmlns:android
		ADDED from /Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from /Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/AndroidManifest.xml:5:5-67
	android:name
		ADDED from /Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/AndroidManifest.xml:5:22-64
uses-permission#android.permission.CAMERA
ADDED from /Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/AndroidManifest.xml:6:5-65
MERGED from [com.journeyapps:zxing-android-embedded:4.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/93bc7b62ed7d90b46adb14d1a6562db2/transformed/jetified-zxing-android-embedded-4.3.0/AndroidManifest.xml:22:5-65
MERGED from [com.journeyapps:zxing-android-embedded:4.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/93bc7b62ed7d90b46adb14d1a6562db2/transformed/jetified-zxing-android-embedded-4.3.0/AndroidManifest.xml:22:5-65
	android:name
		ADDED from /Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/AndroidManifest.xml:6:22-62
uses-feature#android.hardware.camera
ADDED from /Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/AndroidManifest.xml:8:5-10:36
MERGED from [com.journeyapps:zxing-android-embedded:4.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/93bc7b62ed7d90b46adb14d1a6562db2/transformed/jetified-zxing-android-embedded-4.3.0/AndroidManifest.xml:25:5-27:36
MERGED from [com.journeyapps:zxing-android-embedded:4.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/93bc7b62ed7d90b46adb14d1a6562db2/transformed/jetified-zxing-android-embedded-4.3.0/AndroidManifest.xml:25:5-27:36
	android:required
		ADDED from /Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/AndroidManifest.xml:10:9-33
	android:name
		ADDED from /Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/AndroidManifest.xml:9:9-47
application
ADDED from /Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/AndroidManifest.xml:12:5-40:19
INJECTED from /Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/AndroidManifest.xml:12:5-40:19
MERGED from [androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/b4374047bc445910aa5fc28ce452a90c/transformed/jetified-emoji2-1.4.0/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/b4374047bc445910aa5fc28ce452a90c/transformed/jetified-emoji2-1.4.0/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/54692633a1b1700654ac536ed9f93313/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/54692633a1b1700654ac536ed9f93313/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.12/transforms/b52eefb7eefa5c6ad84a8949ac37e1b4/transformed/core-1.12.0/AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.12/transforms/b52eefb7eefa5c6ad84a8949ac37e1b4/transformed/core-1.12.0/AndroidManifest.xml:28:5-89
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/d78b48e300ce6c9a51bb25e989698fc0/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/d78b48e300ce6c9a51bb25e989698fc0/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:24:5-25:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/3fbcbafc1e60471e08c002f3f605bdd0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/3fbcbafc1e60471e08c002f3f605bdd0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/513de38e430506ac1982fabb6728611a/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/513de38e430506ac1982fabb6728611a/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:25:5-31:19
MERGED from [com.journeyapps:zxing-android-embedded:4.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/93bc7b62ed7d90b46adb14d1a6562db2/transformed/jetified-zxing-android-embedded-4.3.0/AndroidManifest.xml:46:5-54:19
MERGED from [com.journeyapps:zxing-android-embedded:4.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/93bc7b62ed7d90b46adb14d1a6562db2/transformed/jetified-zxing-android-embedded-4.3.0/AndroidManifest.xml:46:5-54:19
	android:extractNativeLibs
		INJECTED from /Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.12/transforms/b52eefb7eefa5c6ad84a8949ac37e1b4/transformed/core-1.12.0/AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from /Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/AndroidManifest.xml:20:9-35
	android:label
		ADDED from /Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/AndroidManifest.xml:18:9-41
	android:fullBackupContent
		ADDED from /Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/AndroidManifest.xml:16:9-54
	android:roundIcon
		ADDED from /Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/AndroidManifest.xml:19:9-54
	tools:targetApi
		ADDED from /Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/AndroidManifest.xml:22:9-29
	android:icon
		ADDED from /Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/AndroidManifest.xml:17:9-43
	android:allowBackup
		ADDED from /Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/AndroidManifest.xml:14:9-35
	android:theme
		ADDED from /Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/AndroidManifest.xml:21:9-50
	android:dataExtractionRules
		ADDED from /Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/AndroidManifest.xml:15:9-65
	android:name
		ADDED from /Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/AndroidManifest.xml:13:9-48
activity#com.moneta.sampleapp.MainActivity
ADDED from /Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/AndroidManifest.xml:24:9-32:20
	android:exported
		ADDED from /Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/AndroidManifest.xml:26:13-36
	android:theme
		ADDED from /Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/AndroidManifest.xml:27:13-54
	android:name
		ADDED from /Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/AndroidManifest.xml:25:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from /Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/AndroidManifest.xml:28:13-31:29
action#android.intent.action.MAIN
ADDED from /Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/AndroidManifest.xml:29:17-69
	android:name
		ADDED from /Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/AndroidManifest.xml:29:25-66
category#android.intent.category.LAUNCHER
ADDED from /Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/AndroidManifest.xml:30:17-77
	android:name
		ADDED from /Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/AndroidManifest.xml:30:27-74
activity#com.journeyapps.barcodescanner.CaptureActivity
ADDED from /Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/AndroidManifest.xml:35:9-38:49
MERGED from [com.journeyapps:zxing-android-embedded:4.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/93bc7b62ed7d90b46adb14d1a6562db2/transformed/jetified-zxing-android-embedded-4.3.0/AndroidManifest.xml:47:9-53:63
MERGED from [com.journeyapps:zxing-android-embedded:4.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/93bc7b62ed7d90b46adb14d1a6562db2/transformed/jetified-zxing-android-embedded-4.3.0/AndroidManifest.xml:47:9-53:63
	android:clearTaskOnLaunch
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/93bc7b62ed7d90b46adb14d1a6562db2/transformed/jetified-zxing-android-embedded-4.3.0/AndroidManifest.xml:49:13-45
	android:screenOrientation
		ADDED from /Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/AndroidManifest.xml:37:13-51
		REJECTED from [com.journeyapps:zxing-android-embedded:4.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/93bc7b62ed7d90b46adb14d1a6562db2/transformed/jetified-zxing-android-embedded-4.3.0/AndroidManifest.xml:50:13-56
	android:stateNotNeeded
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/93bc7b62ed7d90b46adb14d1a6562db2/transformed/jetified-zxing-android-embedded-4.3.0/AndroidManifest.xml:51:13-42
	android:windowSoftInputMode
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/93bc7b62ed7d90b46adb14d1a6562db2/transformed/jetified-zxing-android-embedded-4.3.0/AndroidManifest.xml:53:13-60
	android:theme
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/93bc7b62ed7d90b46adb14d1a6562db2/transformed/jetified-zxing-android-embedded-4.3.0/AndroidManifest.xml:52:13-54
	tools:replace
		ADDED from /Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/AndroidManifest.xml:38:13-46
	android:name
		ADDED from /Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/AndroidManifest.xml:36:13-74
uses-sdk
INJECTED from /Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/AndroidManifest.xml reason: use-sdk injection requested
INJECTED from /Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/AndroidManifest.xml
INJECTED from /Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/AndroidManifest.xml
MERGED from [:sdk] /Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sdk/build/intermediates/merged_manifest/release/AndroidManifest.xml:5:5-44
MERGED from [:sdk] /Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sdk/build/intermediates/merged_manifest/release/AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-navigation-compose:1.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/d4b7e4502f5635926ce7e8d1d6916886/transformed/jetified-hilt-navigation-compose-1.1.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-navigation-compose:1.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/d4b7e4502f5635926ce7e8d1d6916886/transformed/jetified-hilt-navigation-compose-1.1.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-navigation:1.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/0d4e809624b7acebf83edd271805b653/transformed/jetified-hilt-navigation-1.1.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-navigation:1.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/0d4e809624b7acebf83edd271805b653/transformed/jetified-hilt-navigation-1.1.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common:2.7.5] /Users/<USER>/.gradle/caches/8.12/transforms/4045b96045558561e5d7dcb95382d7a1/transformed/navigation-common-2.7.5/AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common:2.7.5] /Users/<USER>/.gradle/caches/8.12/transforms/4045b96045558561e5d7dcb95382d7a1/transformed/navigation-common-2.7.5/AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.5] /Users/<USER>/.gradle/caches/8.12/transforms/740445472f0c11b739d7ce803f3c916e/transformed/navigation-runtime-2.7.5/AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.5] /Users/<USER>/.gradle/caches/8.12/transforms/740445472f0c11b739d7ce803f3c916e/transformed/navigation-runtime-2.7.5/AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.5] /Users/<USER>/.gradle/caches/8.12/transforms/927143c12aeee4bd80653c837b51b276/transformed/navigation-common-ktx-2.7.5/AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.5] /Users/<USER>/.gradle/caches/8.12/transforms/927143c12aeee4bd80653c837b51b276/transformed/navigation-common-ktx-2.7.5/AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.5] /Users/<USER>/.gradle/caches/8.12/transforms/4a9155015e7032ffb69f1da53c0b97c3/transformed/navigation-runtime-ktx-2.7.5/AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.5] /Users/<USER>/.gradle/caches/8.12/transforms/4a9155015e7032ffb69f1da53c0b97c3/transformed/navigation-runtime-ktx-2.7.5/AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.7.5] /Users/<USER>/.gradle/caches/8.12/transforms/51a817c1f00e445d65a34785a78c1af6/transformed/jetified-navigation-compose-2.7.5/AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.7.5] /Users/<USER>/.gradle/caches/8.12/transforms/51a817c1f00e445d65a34785a78c1af6/transformed/jetified-navigation-compose-2.7.5/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material3:material3:1.1.2] /Users/<USER>/.gradle/caches/8.12/transforms/9aceb0555898d036bfa3b838a4051259/transformed/jetified-material3-1.1.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3:1.1.2] /Users/<USER>/.gradle/caches/8.12/transforms/9aceb0555898d036bfa3b838a4051259/transformed/jetified-material3-1.1.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-compose:1.8.2] /Users/<USER>/.gradle/caches/8.12/transforms/0b66e125b08c35c9aa341a33681ad6c5/transformed/jetified-activity-compose-1.8.2/AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.8.2] /Users/<USER>/.gradle/caches/8.12/transforms/0b66e125b08c35c9aa341a33681ad6c5/transformed/jetified-activity-compose-1.8.2/AndroidManifest.xml:5:5-44
MERGED from [com.google.dagger:hilt-android:2.48] /Users/<USER>/.gradle/caches/8.12/transforms/1c2f4ae919bb79ac64c14a079e981e48/transformed/jetified-hilt-android-2.48/AndroidManifest.xml:18:3-42
MERGED from [com.google.dagger:hilt-android:2.48] /Users/<USER>/.gradle/caches/8.12/transforms/1c2f4ae919bb79ac64c14a079e981e48/transformed/jetified-hilt-android-2.48/AndroidManifest.xml:18:3-42
MERGED from [androidx.fragment:fragment:1.5.1] /Users/<USER>/.gradle/caches/8.12/transforms/f0abf1c6f8707d5d70b975665ca0a9e2/transformed/fragment-1.5.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.1] /Users/<USER>/.gradle/caches/8.12/transforms/f0abf1c6f8707d5d70b975665ca0a9e2/transformed/fragment-1.5.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] /Users/<USER>/.gradle/caches/8.12/transforms/27be7c78474819404dcfe6814e04e3bd/transformed/jetified-savedstate-ktx-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] /Users/<USER>/.gradle/caches/8.12/transforms/27be7c78474819404dcfe6814e04e3bd/transformed/jetified-savedstate-ktx-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/8.12/transforms/5a62d5c3c3cb53892767ab7d579872be/transformed/jetified-savedstate-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/8.12/transforms/5a62d5c3c3cb53892767ab7d579872be/transformed/jetified-savedstate-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/1fccdfa781da93b153ec2557bb58f606/transformed/loader-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/1fccdfa781da93b153ec2557bb58f606/transformed/loader-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [io.coil-kt:coil-compose:2.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/dc7f8685743c0b1efe60441000c42123/transformed/jetified-coil-compose-2.5.0/AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-compose:2.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/dc7f8685743c0b1efe60441000c42123/transformed/jetified-coil-compose-2.5.0/AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-compose-base:2.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/3d6d01e68a9b9e375facfbd562faf302/transformed/jetified-coil-compose-base-2.5.0/AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-compose-base:2.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/3d6d01e68a9b9e375facfbd562faf302/transformed/jetified-coil-compose-base-2.5.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/4dad4f266d96ad6020cf9740531d3471/transformed/jetified-animation-core-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/4dad4f266d96ad6020cf9740531d3471/transformed/jetified-animation-core-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/f95ef8096eae262e04a55812b6b8c3d0/transformed/jetified-material-icons-core-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/f95ef8096eae262e04a55812b6b8c3d0/transformed/jetified-material-icons-core-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/f48e4d76097b95349d877e87800860a7/transformed/jetified-material-icons-extended-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/f48e4d76097b95349d877e87800860a7/transformed/jetified-material-icons-extended-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/950bd5e44d0de31df548a560080eb744/transformed/jetified-material-ripple-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/950bd5e44d0de31df548a560080eb744/transformed/jetified-material-ripple-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/d147fd09273078aadeca31119680a5ee/transformed/jetified-animation-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/d147fd09273078aadeca31119680a5ee/transformed/jetified-animation-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/517d3d920a97aa59b9adf387945c71b9/transformed/jetified-foundation-layout-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/517d3d920a97aa59b9adf387945c71b9/transformed/jetified-foundation-layout-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/c6f4eab9bf43f1491374830409ee9760/transformed/jetified-foundation-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/c6f4eab9bf43f1491374830409ee9760/transformed/jetified-foundation-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/ca1e941d2bd9bcaafc5957031628a536/transformed/jetified-ui-unit-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/ca1e941d2bd9bcaafc5957031628a536/transformed/jetified-ui-unit-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/0a80abd5c617424f3a501268b55ba7d8/transformed/jetified-ui-geometry-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/0a80abd5c617424f3a501268b55ba7d8/transformed/jetified-ui-geometry-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/7c19b2d242422f17e97f30035e53c3a9/transformed/jetified-ui-util-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/7c19b2d242422f17e97f30035e53c3a9/transformed/jetified-ui-util-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/b321e078f9f8c3d35e824ddce3fcf9f6/transformed/jetified-ui-tooling-preview-release/AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/b321e078f9f8c3d35e824ddce3fcf9f6/transformed/jetified-ui-tooling-preview-release/AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/e04f21f9f9708bebbfbb6e5f55174392/transformed/jetified-ui-graphics-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/e04f21f9f9708bebbfbb6e5f55174392/transformed/jetified-ui-graphics-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/1476e5beafce493c017134248d9b3796/transformed/jetified-ui-text-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/1476e5beafce493c017134248d9b3796/transformed/jetified-ui-text-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/b4374047bc445910aa5fc28ce452a90c/transformed/jetified-emoji2-1.4.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/b4374047bc445910aa5fc28ce452a90c/transformed/jetified-emoji2-1.4.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/54692633a1b1700654ac536ed9f93313/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/54692633a1b1700654ac536ed9f93313/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/c403f1a2cc8aaffaa2bf644415e14888/transformed/jetified-lifecycle-livedata-core-ktx-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/c403f1a2cc8aaffaa2bf644415e14888/transformed/jetified-lifecycle-livedata-core-ktx-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/378041e336fdfb1ee8162302a0fad1d3/transformed/lifecycle-livedata-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/378041e336fdfb1ee8162302a0fad1d3/transformed/lifecycle-livedata-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/4df479c468ae771903bf93ee550a95de/transformed/lifecycle-livedata-core-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/4df479c468ae771903bf93ee550a95de/transformed/lifecycle-livedata-core-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/259d0b71e1cd1204b212da6f98223a81/transformed/lifecycle-viewmodel-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/259d0b71e1cd1204b212da6f98223a81/transformed/lifecycle-viewmodel-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/76e32685a77ba7330edacfee9d4734ad/transformed/jetified-lifecycle-viewmodel-savedstate-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/76e32685a77ba7330edacfee9d4734ad/transformed/jetified-lifecycle-viewmodel-savedstate-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/15863efbebeaf1b382dd6173661e2892/transformed/jetified-lifecycle-viewmodel-ktx-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/15863efbebeaf1b382dd6173661e2892/transformed/jetified-lifecycle-viewmodel-ktx-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil:2.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/9a5d94824a7efe14d375ef0034680f90/transformed/jetified-coil-2.5.0/AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil:2.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/9a5d94824a7efe14d375ef0034680f90/transformed/jetified-coil-2.5.0/AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-base:2.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/bf7ce8bc449644adbbe7701607436e97/transformed/jetified-coil-base-2.5.0/AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-base:2.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/bf7ce8bc449644adbbe7701607436e97/transformed/jetified-coil-base-2.5.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/b9659bba57388a7c47d83f18237ab725/transformed/lifecycle-runtime-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/b9659bba57388a7c47d83f18237ab725/transformed/lifecycle-runtime-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/da12a843988567b4171b5ec6f3097b15/transformed/jetified-lifecycle-runtime-compose-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/da12a843988567b4171b5ec6f3097b15/transformed/jetified-lifecycle-runtime-compose-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/fee67b8b02e8a025f663d6f220b695da/transformed/jetified-lifecycle-runtime-ktx-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/fee67b8b02e8a025f663d6f220b695da/transformed/jetified-lifecycle-runtime-ktx-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/004d42a4e87d4593fffb68471454c2c2/transformed/jetified-lifecycle-viewmodel-compose-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/004d42a4e87d4593fffb68471454c2c2/transformed/jetified-lifecycle-viewmodel-compose-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.accompanist:accompanist-drawablepainter:0.32.0] /Users/<USER>/.gradle/caches/8.12/transforms/0dd2fb74628537fe6ea0ea0a76580b0b/transformed/jetified-accompanist-drawablepainter-0.32.0/AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-drawablepainter:0.32.0] /Users/<USER>/.gradle/caches/8.12/transforms/0dd2fb74628537fe6ea0ea0a76580b0b/transformed/jetified-accompanist-drawablepainter-0.32.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.compose.ui:ui-android:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/6dcd5d8b64257e5a867f481fb385b30c/transformed/jetified-ui-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/6dcd5d8b64257e5a867f481fb385b30c/transformed/jetified-ui-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.2] /Users/<USER>/.gradle/caches/8.12/transforms/c5c812536c1dc1eac50576521d3a78c0/transformed/jetified-activity-ktx-1.8.2/AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.2] /Users/<USER>/.gradle/caches/8.12/transforms/c5c812536c1dc1eac50576521d3a78c0/transformed/jetified-activity-ktx-1.8.2/AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.8.2] /Users/<USER>/.gradle/caches/8.12/transforms/e7f374bb96735f0f7f4fe25b91aab5b5/transformed/jetified-activity-1.8.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.2] /Users/<USER>/.gradle/caches/8.12/transforms/e7f374bb96735f0f7f4fe25b91aab5b5/transformed/jetified-activity-1.8.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/07dc29200c028a86837fa262ff63b3f9/transformed/viewpager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/07dc29200c028a86837fa262ff63b3f9/transformed/viewpager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.autofill:autofill:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/48c682baeaed42a100799b805e4e6acc/transformed/jetified-autofill-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/48c682baeaed42a100799b805e4e6acc/transformed/jetified-autofill-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/d94e55162f72843d9568bebe6e3aad8e/transformed/customview-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/d94e55162f72843d9568bebe6e3aad8e/transformed/customview-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] /Users/<USER>/.gradle/caches/8.12/transforms/c2fd60454ffad06625074a3fcabe3477/transformed/jetified-appcompat-resources-1.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] /Users/<USER>/.gradle/caches/8.12/transforms/c2fd60454ffad06625074a3fcabe3477/transformed/jetified-appcompat-resources-1.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/465801801c9464eb74caeab10dcb173e/transformed/vectordrawable-animated-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/465801801c9464eb74caeab10dcb173e/transformed/vectordrawable-animated-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/a59af7079a95d20f0cffbb5232f06ded/transformed/vectordrawable-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/a59af7079a95d20f0cffbb5232f06ded/transformed/vectordrawable-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.12/transforms/b52eefb7eefa5c6ad84a8949ac37e1b4/transformed/core-1.12.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.12/transforms/b52eefb7eefa5c6ad84a8949ac37e1b4/transformed/core-1.12.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/e2a3a2e00d33eaf6b3bcebf95e42ffaf/transformed/jetified-customview-poolingcontainer-1.0.0/AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/e2a3a2e00d33eaf6b3bcebf95e42ffaf/transformed/jetified-customview-poolingcontainer-1.0.0/AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.12.0] /Users/<USER>/.gradle/caches/8.12/transforms/7ebdbedc48c0fe6597efbcb4ca42db67/transformed/jetified-core-ktx-1.12.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.12.0] /Users/<USER>/.gradle/caches/8.12/transforms/7ebdbedc48c0fe6597efbcb4ca42db67/transformed/jetified-core-ktx-1.12.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/eddc93bfd5629ef66100108312dd6ce5/transformed/jetified-runtime-saveable-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/eddc93bfd5629ef66100108312dd6ce5/transformed/jetified-runtime-saveable-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/b1b7f44aee1cf79ac965ecc3bcb423e1/transformed/jetified-runtime-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/b1b7f44aee1cf79ac965ecc3bcb423e1/transformed/jetified-runtime-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/59e52510e7060bed3731d040990aa23c/transformed/jetified-annotation-experimental-1.3.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/59e52510e7060bed3731d040990aa23c/transformed/jetified-annotation-experimental-1.3.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/dba0856e066e5109e479c688e8875839/transformed/interpolator-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/dba0856e066e5109e479c688e8875839/transformed/interpolator-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/d78b48e300ce6c9a51bb25e989698fc0/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/d78b48e300ce6c9a51bb25e989698fc0/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/a315a6a6a95a04cd876c6fca96b939ca/transformed/core-runtime-2.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/a315a6a6a95a04cd876c6fca96b939ca/transformed/core-runtime-2.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/3fbcbafc1e60471e08c002f3f605bdd0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/3fbcbafc1e60471e08c002f3f605bdd0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/513de38e430506ac1982fabb6728611a/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/513de38e430506ac1982fabb6728611a/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/506c27cdfd10f86a4453bebc87767a02/transformed/jetified-tracing-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/506c27cdfd10f86a4453bebc87767a02/transformed/jetified-tracing-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] /Users/<USER>/.gradle/caches/8.12/transforms/0540caa3e57c85f414a13e37573913ff/transformed/exifinterface-1.3.6/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] /Users/<USER>/.gradle/caches/8.12/transforms/0540caa3e57c85f414a13e37573913ff/transformed/exifinterface-1.3.6/AndroidManifest.xml:20:5-22:41
MERGED from [com.journeyapps:zxing-android-embedded:4.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/93bc7b62ed7d90b46adb14d1a6562db2/transformed/jetified-zxing-android-embedded-4.3.0/AndroidManifest.xml:20:5-44
MERGED from [com.journeyapps:zxing-android-embedded:4.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/93bc7b62ed7d90b46adb14d1a6562db2/transformed/jetified-zxing-android-embedded-4.3.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.dagger:dagger-lint-aar:2.48] /Users/<USER>/.gradle/caches/8.12/transforms/b8815893253a0c979a6f901e3355b6e0/transformed/jetified-dagger-lint-aar-2.48/AndroidManifest.xml:18:3-42
MERGED from [com.google.dagger:dagger-lint-aar:2.48] /Users/<USER>/.gradle/caches/8.12/transforms/b8815893253a0c979a6f901e3355b6e0/transformed/jetified-dagger-lint-aar-2.48/AndroidManifest.xml:18:3-42
	android:targetSdkVersion
		INJECTED from /Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/AndroidManifest.xml
	android:minSdkVersion
		INJECTED from /Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/AndroidManifest.xml
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/b4374047bc445910aa5fc28ce452a90c/transformed/jetified-emoji2-1.4.0/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/54692633a1b1700654ac536ed9f93313/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/54692633a1b1700654ac536ed9f93313/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/3fbcbafc1e60471e08c002f3f605bdd0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/3fbcbafc1e60471e08c002f3f605bdd0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/513de38e430506ac1982fabb6728611a/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/513de38e430506ac1982fabb6728611a/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/b4374047bc445910aa5fc28ce452a90c/transformed/jetified-emoji2-1.4.0/AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/b4374047bc445910aa5fc28ce452a90c/transformed/jetified-emoji2-1.4.0/AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/b4374047bc445910aa5fc28ce452a90c/transformed/jetified-emoji2-1.4.0/AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/b4374047bc445910aa5fc28ce452a90c/transformed/jetified-emoji2-1.4.0/AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/b4374047bc445910aa5fc28ce452a90c/transformed/jetified-emoji2-1.4.0/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/b4374047bc445910aa5fc28ce452a90c/transformed/jetified-emoji2-1.4.0/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/b4374047bc445910aa5fc28ce452a90c/transformed/jetified-emoji2-1.4.0/AndroidManifest.xml:30:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/54692633a1b1700654ac536ed9f93313/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/54692633a1b1700654ac536ed9f93313/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/54692633a1b1700654ac536ed9f93313/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:30:17-78
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.12/transforms/b52eefb7eefa5c6ad84a8949ac37e1b4/transformed/core-1.12.0/AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.12/transforms/b52eefb7eefa5c6ad84a8949ac37e1b4/transformed/core-1.12.0/AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.12/transforms/b52eefb7eefa5c6ad84a8949ac37e1b4/transformed/core-1.12.0/AndroidManifest.xml:23:9-81
permission#com.moneta.sampleapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.12/transforms/b52eefb7eefa5c6ad84a8949ac37e1b4/transformed/core-1.12.0/AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.12/transforms/b52eefb7eefa5c6ad84a8949ac37e1b4/transformed/core-1.12.0/AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.12/transforms/b52eefb7eefa5c6ad84a8949ac37e1b4/transformed/core-1.12.0/AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.12/transforms/b52eefb7eefa5c6ad84a8949ac37e1b4/transformed/core-1.12.0/AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.12/transforms/b52eefb7eefa5c6ad84a8949ac37e1b4/transformed/core-1.12.0/AndroidManifest.xml:26:22-94
uses-permission#com.moneta.sampleapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.12/transforms/b52eefb7eefa5c6ad84a8949ac37e1b4/transformed/core-1.12.0/AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.12/transforms/b52eefb7eefa5c6ad84a8949ac37e1b4/transformed/core-1.12.0/AndroidManifest.xml:26:22-94
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/3fbcbafc1e60471e08c002f3f605bdd0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/3fbcbafc1e60471e08c002f3f605bdd0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/3fbcbafc1e60471e08c002f3f605bdd0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/3fbcbafc1e60471e08c002f3f605bdd0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/3fbcbafc1e60471e08c002f3f605bdd0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/3fbcbafc1e60471e08c002f3f605bdd0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/3fbcbafc1e60471e08c002f3f605bdd0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/3fbcbafc1e60471e08c002f3f605bdd0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/3fbcbafc1e60471e08c002f3f605bdd0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/3fbcbafc1e60471e08c002f3f605bdd0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/3fbcbafc1e60471e08c002f3f605bdd0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/3fbcbafc1e60471e08c002f3f605bdd0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/3fbcbafc1e60471e08c002f3f605bdd0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/3fbcbafc1e60471e08c002f3f605bdd0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/3fbcbafc1e60471e08c002f3f605bdd0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/3fbcbafc1e60471e08c002f3f605bdd0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/3fbcbafc1e60471e08c002f3f605bdd0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/3fbcbafc1e60471e08c002f3f605bdd0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/3fbcbafc1e60471e08c002f3f605bdd0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/3fbcbafc1e60471e08c002f3f605bdd0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/3fbcbafc1e60471e08c002f3f605bdd0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:50:25-92
uses-feature#android.hardware.camera.front
ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/93bc7b62ed7d90b46adb14d1a6562db2/transformed/jetified-zxing-android-embedded-4.3.0/AndroidManifest.xml:28:5-30:36
	android:required
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/93bc7b62ed7d90b46adb14d1a6562db2/transformed/jetified-zxing-android-embedded-4.3.0/AndroidManifest.xml:30:9-33
	android:name
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/93bc7b62ed7d90b46adb14d1a6562db2/transformed/jetified-zxing-android-embedded-4.3.0/AndroidManifest.xml:29:9-53
uses-feature#android.hardware.camera.autofocus
ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/93bc7b62ed7d90b46adb14d1a6562db2/transformed/jetified-zxing-android-embedded-4.3.0/AndroidManifest.xml:33:5-35:36
	android:required
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/93bc7b62ed7d90b46adb14d1a6562db2/transformed/jetified-zxing-android-embedded-4.3.0/AndroidManifest.xml:35:9-33
	android:name
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/93bc7b62ed7d90b46adb14d1a6562db2/transformed/jetified-zxing-android-embedded-4.3.0/AndroidManifest.xml:34:9-57
uses-feature#android.hardware.camera.flash
ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/93bc7b62ed7d90b46adb14d1a6562db2/transformed/jetified-zxing-android-embedded-4.3.0/AndroidManifest.xml:36:5-38:36
	android:required
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/93bc7b62ed7d90b46adb14d1a6562db2/transformed/jetified-zxing-android-embedded-4.3.0/AndroidManifest.xml:38:9-33
	android:name
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/93bc7b62ed7d90b46adb14d1a6562db2/transformed/jetified-zxing-android-embedded-4.3.0/AndroidManifest.xml:37:9-53
uses-feature#android.hardware.screen.landscape
ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/93bc7b62ed7d90b46adb14d1a6562db2/transformed/jetified-zxing-android-embedded-4.3.0/AndroidManifest.xml:39:5-41:36
	android:required
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/93bc7b62ed7d90b46adb14d1a6562db2/transformed/jetified-zxing-android-embedded-4.3.0/AndroidManifest.xml:41:9-33
	android:name
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/93bc7b62ed7d90b46adb14d1a6562db2/transformed/jetified-zxing-android-embedded-4.3.0/AndroidManifest.xml:40:9-57
uses-feature#android.hardware.wifi
ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/93bc7b62ed7d90b46adb14d1a6562db2/transformed/jetified-zxing-android-embedded-4.3.0/AndroidManifest.xml:42:5-44:36
	android:required
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/93bc7b62ed7d90b46adb14d1a6562db2/transformed/jetified-zxing-android-embedded-4.3.0/AndroidManifest.xml:44:9-33
	android:name
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/93bc7b62ed7d90b46adb14d1a6562db2/transformed/jetified-zxing-android-embedded-4.3.0/AndroidManifest.xml:43:9-45
