package dagger.hilt.internal.aggregatedroot.codegen;

import dagger.hilt.android.HiltAndroidApp;
import dagger.hilt.internal.aggregatedroot.AggregatedRoot;

/**
 * This class should only be referenced by generated code! This class aggregates information across multiple compilations.
 */
@AggregatedRoot(
    root = "com.moneta.sampleapp.MonetaSampleApplication",
    rootPackage = "com.moneta.sampleapp",
    originatingRoot = "com.moneta.sampleapp.MonetaSampleApplication",
    originatingRootPackage = "com.moneta.sampleapp",
    rootAnnotation = HiltAndroidApp.class,
    rootSimpleNames = "MonetaSampleApplication",
    originatingRootSimpleNames = "MonetaSampleApplication"
)
public class _com_moneta_sampleapp_MonetaSampleApplication {
}
