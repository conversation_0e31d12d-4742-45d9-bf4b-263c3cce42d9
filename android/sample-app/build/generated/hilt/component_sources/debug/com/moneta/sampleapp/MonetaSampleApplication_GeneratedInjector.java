package com.moneta.sampleapp;

import dagger.hilt.InstallIn;
import dagger.hilt.codegen.OriginatingElement;
import dagger.hilt.components.SingletonComponent;
import dagger.hilt.internal.GeneratedEntryPoint;

@OriginatingElement(
    topLevelClass = MonetaSampleApplication.class
)
@GeneratedEntryPoint
@InstallIn(SingletonComponent.class)
public interface MonetaSampleApplication_GeneratedInjector {
  void injectMonetaSampleApplication(MonetaSampleApplication monetaSampleApplication);
}
