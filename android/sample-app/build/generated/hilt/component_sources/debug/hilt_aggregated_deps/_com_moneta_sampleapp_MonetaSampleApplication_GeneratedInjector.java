package hilt_aggregated_deps;

import dagger.hilt.processor.internal.aggregateddeps.AggregatedDeps;

/**
 * This class should only be referenced by generated code! This class aggregates information across multiple compilations.
 */
@AggregatedDeps(
    components = "dagger.hilt.components.SingletonComponent",
    entryPoints = "com.moneta.sampleapp.MonetaSampleApplication_GeneratedInjector"
)
public class _com_moneta_sampleapp_MonetaSampleApplication_GeneratedInjector {
}
