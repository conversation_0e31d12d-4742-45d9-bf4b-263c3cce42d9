package com.moneta.sampleapp.ui.screens.initialization;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class SDKInitializationViewModel_Factory implements Factory<SDKInitializationViewModel> {
  @Override
  public SDKInitializationViewModel get() {
    return newInstance();
  }

  public static SDKInitializationViewModel_Factory create() {
    return InstanceHolder.INSTANCE;
  }

  public static SDKInitializationViewModel newInstance() {
    return new SDKInitializationViewModel();
  }

  private static final class InstanceHolder {
    private static final SDKInitializationViewModel_Factory INSTANCE = new SDKInitializationViewModel_Factory();
  }
}
