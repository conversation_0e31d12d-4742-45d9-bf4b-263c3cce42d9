<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/res"><file name="ic_launcher_foreground" path="/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/res/drawable/ic_launcher_foreground.xml" qualifiers="" type="drawable"/><file name="ic_launcher_background" path="/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/res/drawable/ic_launcher_background.xml" qualifiers="" type="drawable"/><file path="/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/res/values/colors.xml" qualifiers=""><color name="purple_200">#FFBB86FC</color><color name="purple_500">#FF6200EE</color><color name="purple_700">#FF3700B3</color><color name="teal_200">#FF03DAC5</color><color name="teal_700">#FF018786</color><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color><color name="moneta_primary">#FF1976D2</color><color name="moneta_primary_variant">#FF1565C0</color><color name="moneta_secondary">#FF03DAC6</color><color name="moneta_background">#FFFFFFFF</color><color name="moneta_surface">#FFFFFFFF</color><color name="moneta_error">#FFB00020</color><color name="moneta_on_primary">#FFFFFFFF</color><color name="moneta_on_secondary">#FF000000</color><color name="moneta_on_background">#FF000000</color><color name="moneta_on_surface">#FF000000</color><color name="moneta_on_error">#FFFFFFFF</color></file><file path="/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/res/values/themes.xml" qualifiers=""><style name="Theme.MonetaSample" parent="android:Theme.Material.Light.NoActionBar"/></file><file path="/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/res/values/strings.xml" qualifiers=""><string name="app_name">Moneta Sample</string><string name="nav_dashboard">Dashboard</string><string name="nav_transactions">Transactions</string><string name="nav_recommendations">Recommendations</string><string name="nav_publisher_auth">Publisher Auth</string><string name="nav_settings">Settings</string><string name="onboarding_title">Welcome to Moneta</string><string name="onboarding_subtitle">Get started by setting up your account</string><string name="start_onboarding">Start Onboarding</string><string name="complete_onboarding">Complete Onboarding</string><string name="user_code_label">Your User Code:</string><string name="device_code_label">Device Code:</string><string name="onboarding_instructions">Please use this code to complete your onboarding on the web portal</string><string name="dashboard_title">Dashboard</string><string name="balance_label">Current Balance</string><string name="recent_transactions">Recent Transactions</string><string name="view_all_transactions">View All</string><string name="transactions_title">Transactions</string><string name="transaction_amount">Amount: %1$s</string><string name="transaction_publisher">Publisher: %1$s</string><string name="transaction_date">Date: %1$s</string><string name="transaction_status">Status: %1$s</string><string name="no_transactions">No transactions found</string><string name="recommendations_title">Recommendations</string><string name="no_recommendations">No recommendations available</string><string name="recommendation_source">Source: %1$s</string><string name="publisher_auth_title">Publisher Authentication</string><string name="scan_qr_code">Scan QR Code</string><string name="qr_code_instructions">Scan the QR code from the publisher to authenticate</string><string name="settings_title">Settings</string><string name="user_profile">User Profile</string><string name="content_preferences">Content Preferences</string><string name="about">About</string><string name="loading">Loading...</string><string name="error_occurred">An error occurred</string><string name="retry">Retry</string><string name="ok">OK</string><string name="cancel">Cancel</string><string name="save">Save</string><string name="error_network">Network error. Please check your connection.</string><string name="error_sdk_not_initialized">SDK not initialized. Please restart the app.</string><string name="error_onboarding_failed">Onboarding failed. Please try again.</string><string name="error_authentication_failed">Authentication failed. Please try again.</string></file><file name="backup_rules" path="/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/res/xml/backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/res/xml/data_extraction_rules.xml" qualifiers="" type="xml"/><file name="ic_launcher" path="/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/res/mipmap-anydpi-v26/ic_launcher.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher_round" path="/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/res/mipmap-anydpi-v26/ic_launcher_round.xml" qualifiers="anydpi-v26" type="mipmap"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="release$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/release/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="release" generated-set="release$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/release/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/build/generated/res/resValues/release"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/build/generated/res/resValues/release"/></dataSet><mergedItems/></merger>