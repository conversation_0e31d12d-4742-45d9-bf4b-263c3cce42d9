{"logs": [{"outputFile": "com.moneta.sampleapp.sample-app-mergeReleaseResources-54:/values-si/values-si.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.12/transforms/b52eefb7eefa5c6ad84a8949ac37e1b4/transformed/core-1.12.0/res/values-si/values-si.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,260,365,470,569,673,787", "endColumns": "101,102,104,104,98,103,113,100", "endOffsets": "152,255,360,465,564,668,782,883"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "550,652,755,860,965,1064,1168,6300", "endColumns": "101,102,104,104,98,103,113,100", "endOffsets": "647,750,855,960,1059,1163,1277,6396"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/6dcd5d8b64257e5a867f481fb385b30c/transformed/jetified-ui-release/res/values-si/values-si.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,194,277,376,475,557,642,733,819,899,976,1051,1130,1212,1285,1366,1433", "endColumns": "88,82,98,98,81,84,90,85,79,76,74,78,81,72,80,66,117", "endOffsets": "189,272,371,470,552,637,728,814,894,971,1046,1125,1207,1280,1361,1428,1546"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1282,1371,4973,5072,5243,5405,5490,5581,5667,5747,5824,5899,5978,6142,6491,6572,6639", "endColumns": "88,82,98,98,81,84,90,85,79,76,74,78,81,72,80,66,117", "endOffsets": "1366,1449,5067,5166,5320,5485,5576,5662,5742,5819,5894,5973,6055,6210,6567,6634,6752"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/9aceb0555898d036bfa3b838a4051259/transformed/jetified-material3-1.1.2/res/values-si/values-si.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,167,280,387,500,578,670,776,901,1011,1146,1226,1328,1415,1504,1614,1738,1845,1969,2091,2220,2389,2509,2622,2741,2859,2947,3038,3153,3270,3367,3466,3566,3692,3824,3927,4019,4091,4171,4253,4338,4428,4507,4586,4681,4777,4865,4962,5046,5149,5247,5354,5471,5549,5653", "endColumns": "111,112,106,112,77,91,105,124,109,134,79,101,86,88,109,123,106,123,121,128,168,119,112,118,117,87,90,114,116,96,98,99,125,131,102,91,71,79,81,84,89,78,78,94,95,87,96,83,102,97,106,116,77,103,94", "endOffsets": "162,275,382,495,573,665,771,896,1006,1141,1221,1323,1410,1499,1609,1733,1840,1964,2086,2215,2384,2504,2617,2736,2854,2942,3033,3148,3265,3362,3461,3561,3687,3819,3922,4014,4086,4166,4248,4333,4423,4502,4581,4676,4772,4860,4957,5041,5144,5242,5349,5466,5544,5648,5743"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,217,330,437,1454,1532,1624,1730,1855,1965,2100,2180,2282,2369,2458,2568,2692,2799,2923,3045,3174,3343,3463,3576,3695,3813,3901,3992,4107,4224,4321,4420,4520,4646,4778,4881,5171,5325,6060,6215,6401,6757,6836,6915,7010,7106,7194,7291,7375,7478,7576,7683,7800,7878,7982", "endColumns": "111,112,106,112,77,91,105,124,109,134,79,101,86,88,109,123,106,123,121,128,168,119,112,118,117,87,90,114,116,96,98,99,125,131,102,91,71,79,81,84,89,78,78,94,95,87,96,83,102,97,106,116,77,103,94", "endOffsets": "212,325,432,545,1527,1619,1725,1850,1960,2095,2175,2277,2364,2453,2563,2687,2794,2918,3040,3169,3338,3458,3571,3690,3808,3896,3987,4102,4219,4316,4415,4515,4641,4773,4876,4968,5238,5400,6137,6295,6486,6831,6910,7005,7101,7189,7286,7370,7473,7571,7678,7795,7873,7977,8072"}}]}]}