{"logs": [{"outputFile": "com.moneta.sampleapp.sample-app-mergeReleaseResources-54:/values/values.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.12/transforms/f0abf1c6f8707d5d70b975665ca0a9e2/transformed/fragment-1.5.1/res/values/values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "98,107,128,419,424", "startColumns": "4,4,4,4,4", "startOffsets": "5970,6419,7550,23852,24022", "endLines": "98,107,128,423,427", "endColumns": "56,64,63,24,24", "endOffsets": "6022,6479,7609,24017,24166"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/b9659bba57388a7c47d83f18237ab725/transformed/lifecycle-runtime-2.7.0/res/values/values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "124", "startColumns": "4", "startOffsets": "7343", "endColumns": "42", "endOffsets": "7381"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/6dcd5d8b64257e5a867f481fb385b30c/transformed/jetified-ui-release/res/values/values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,60,63", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,209,268,328,388,448,508,568,628,688,748,808,868,927,987,1047,1107,1167,1227,1287,1347,1407,1467,1527,1586,1646,1706,1765,1824,1883,1942,2001,2060,2134,2192,2247,2298,2353,2406,2471,2525,2591,2692,2750,2802,2862,2924,2978,3014,3048,3098,3152,3198,3245,3281,3371,3483,3594", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,59,62,66", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,35,33,49,53,45,46,35,89,12,12,12", "endOffsets": "204,263,323,383,443,503,563,623,683,743,803,863,922,982,1042,1102,1162,1222,1282,1342,1402,1462,1522,1581,1641,1701,1760,1819,1878,1937,1996,2055,2129,2187,2242,2293,2348,2401,2466,2520,2586,2687,2745,2797,2857,2919,2973,3009,3043,3093,3147,3193,3240,3276,3366,3478,3589,3784"}, "to": {"startLines": "62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,96,97,99,100,129,157,158,198,199,203,210,211,228,231,232,234,240,241,249,255,256,257,284,287,290", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3823,3882,3941,4001,4061,4121,4181,4241,4301,4361,4421,4481,4541,4600,4660,4720,4780,4840,4900,4960,5020,5080,5140,5200,5259,5319,5379,5438,5497,5556,5615,5674,5733,5857,5915,6027,6078,7614,9360,9425,12253,12319,12593,13130,13182,14707,14911,14965,15035,15513,15563,15999,16351,16398,16434,18241,18353,18464", "endLines": "62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,96,97,99,100,129,157,158,198,199,203,210,211,228,231,232,234,240,241,249,255,256,257,286,289,293", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,35,33,49,53,45,46,35,89,12,12,12", "endOffsets": "3877,3936,3996,4056,4116,4176,4236,4296,4356,4416,4476,4536,4595,4655,4715,4775,4835,4895,4955,5015,5075,5135,5195,5254,5314,5374,5433,5492,5551,5610,5669,5728,5802,5910,5965,6073,6128,7662,9420,9474,12314,12415,12646,13177,13237,14764,14960,14996,15064,15558,15612,16040,16393,16429,16519,18348,18459,18654"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/e2a3a2e00d33eaf6b3bcebf95e42ffaf/transformed/jetified-customview-poolingcontainer-1.0.0/res/values/values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,109", "endColumns": "53,66", "endOffsets": "104,171"}, "to": {"startLines": "101,105", "startColumns": "4,4", "startOffsets": "6133,6310", "endColumns": "53,66", "endOffsets": "6182,6372"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/513de38e430506ac1982fabb6728611a/transformed/jetified-startup-runtime-1.1.1/res/values/values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "141", "startColumns": "4", "startOffsets": "8226", "endColumns": "82", "endOffsets": "8304"}}, {"source": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/res/values/colors.xml", "from": {"startLines": "7,14,16,19,21,17,18,20,11,12,13,15,2,3,4,5,6,8", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "286,571,676,835,946,725,779,892,408,459,518,625,55,102,149,196,241,328", "endColumns": "41,53,48,56,51,53,55,53,50,58,52,50,46,46,46,44,44,41", "endOffsets": "323,620,720,887,993,774,830,941,454,513,566,671,97,144,191,236,281,365"}, "to": {"startLines": "5,8,9,10,11,12,13,14,15,16,17,18,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "370,543,597,646,703,755,809,865,919,970,1029,1082,1265,1312,1359,1406,1451,1496", "endColumns": "41,53,48,56,51,53,55,53,50,58,52,50,46,46,46,44,44,41", "endOffsets": "407,592,641,698,750,804,860,914,965,1024,1077,1128,1307,1354,1401,1446,1491,1533"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/bf7ce8bc449644adbbe7701607436e97/transformed/jetified-coil-base-2.5.0/res/values/values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "95", "startColumns": "4", "startOffsets": "5807", "endColumns": "49", "endOffsets": "5852"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/259d0b71e1cd1204b212da6f98223a81/transformed/lifecycle-viewmodel-2.7.0/res/values/values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "127", "startColumns": "4", "startOffsets": "7500", "endColumns": "49", "endOffsets": "7545"}}, {"source": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/res/values/themes.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "89", "endOffsets": "140"}, "to": {"startLines": "305", "startColumns": "4", "startOffsets": "19417", "endColumns": "88", "endOffsets": "19501"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/c2fd60454ffad06625074a3fcabe3477/transformed/jetified-appcompat-resources-1.6.1/res/values/values.xml", "from": {"startLines": "2,18,24,34,50", "startColumns": "4,4,4,4,4", "startOffsets": "55,480,658,942,1353", "endLines": "17,23,33,49,53", "endColumns": "24,24,24,24,24", "endOffsets": "475,653,937,1348,1475"}, "to": {"startLines": "317,333,339,506,522", "startColumns": "4,4,4,4,4", "startOffsets": "20049,20474,20652,26695,27106", "endLines": "332,338,348,521,525", "endColumns": "24,24,24,24,24", "endOffsets": "20469,20647,20931,27101,27228"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/e7f374bb96735f0f7f4fe25b91aab5b5/transformed/jetified-activity-1.8.2/res/values/values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "106,125", "startColumns": "4,4", "startOffsets": "6377,7386", "endColumns": "41,59", "endOffsets": "6414,7441"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/93bc7b62ed7d90b46adb14d1a6562db2/transformed/jetified-zxing-android-embedded-4.3.0/res/values/values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,38,48,55", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,127,188,252,317,382,436,490,544,603,661,708,757,805,847,896,948,1006,1056,1110,1176,1246,1343,1460,1556,1717,1830,1916,2063,2162,2309,2368,2415,2559,2672,2772,3228,3593", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,37,47,54,57", "endColumns": "71,60,63,64,64,53,53,53,58,57,46,48,47,41,48,51,57,49,53,65,69,96,116,95,160,112,85,146,98,146,58,46,143,112,10,22,22,22", "endOffsets": "122,183,247,312,377,431,485,539,598,656,703,752,800,842,891,943,1001,1051,1105,1171,1241,1338,1455,1551,1712,1825,1911,2058,2157,2304,2363,2410,2554,2667,2767,3223,3588,3712"}, "to": {"startLines": "27,28,29,30,31,32,33,34,35,36,130,131,132,133,134,135,136,137,138,143,200,212,213,214,215,216,217,218,219,220,280,281,282,283,308,526,536,543", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1538,1610,1671,1735,1800,1865,1919,1973,2027,2086,7667,7714,7763,7811,7853,7902,7954,8012,8062,8360,12420,13242,13339,13456,13552,13713,13826,13912,14059,14158,17878,17937,17984,18128,19645,27233,27689,28054", "endLines": "27,28,29,30,31,32,33,34,35,36,130,131,132,133,134,135,136,137,138,143,200,212,213,214,215,216,217,218,219,220,280,281,282,283,309,535,542,545", "endColumns": "71,60,63,64,64,53,53,53,58,57,46,48,47,41,48,51,57,49,53,65,69,96,116,95,160,112,85,146,98,146,58,46,143,112,10,22,22,22", "endOffsets": "1605,1666,1730,1795,1860,1914,1968,2022,2081,2139,7709,7758,7806,7848,7897,7949,8007,8057,8111,8421,12485,13334,13451,13547,13708,13821,13907,14054,14153,14300,17932,17979,18123,18236,19740,27684,28049,28173"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/740445472f0c11b739d7ce803f3c916e/transformed/navigation-runtime-2.7.5/res/values/values.xml", "from": {"startLines": "2,3,10,13", "startColumns": "4,4,4,4", "startOffsets": "55,108,412,527", "endLines": "2,9,12,15", "endColumns": "52,24,24,24", "endOffsets": "103,407,522,637"}, "to": {"startLines": "104,310,495,498", "startColumns": "4,4,4,4", "startOffsets": "6257,19745,26283,26398", "endLines": "104,316,497,500", "endColumns": "52,24,24,24", "endOffsets": "6305,20044,26393,26508"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/4045b96045558561e5d7dcb95382d7a1/transformed/navigation-common-2.7.5/res/values/values.xml", "from": {"startLines": "2,15,21,27,30", "startColumns": "4,4,4,4,4", "startOffsets": "55,694,938,1185,1318", "endLines": "14,20,26,29,34", "endColumns": "24,24,24,24,24", "endOffsets": "689,933,1180,1313,1495"}, "to": {"startLines": "467,480,486,492,501", "startColumns": "4,4,4,4,4", "startOffsets": "25020,25659,25903,26150,26513", "endLines": "479,485,491,494,505", "endColumns": "24,24,24,24,24", "endOffsets": "25654,25898,26145,26278,26690"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/5a62d5c3c3cb53892767ab7d579872be/transformed/jetified-savedstate-1.2.1/res/values/values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "126", "startColumns": "4", "startOffsets": "7446", "endColumns": "53", "endOffsets": "7495"}}, {"source": "/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/res/values/strings.xml", "from": {"startLines": "48,2,22,55,15,47,21,17,62,59,52,61,60,51,5,8,7,9,6,36,32,54,18,13,12,40,42,23,37,35,53,56,41,45,14,28,30,29,31,27,16,46,24", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2336,55,1050,2583,662,2268,996,790,2963,2688,2448,2874,2775,2401,135,309,245,371,187,1734,1568,2549,849,513,450,1906,2034,1108,1810,1668,2509,2625,1980,2162,600,1326,1452,1386,1508,1266,730,2214,1176", "endColumns": "39,50,57,41,67,67,53,58,96,86,60,88,98,46,51,61,63,49,57,75,65,33,118,86,62,73,100,67,62,65,39,37,53,51,61,59,55,65,59,59,59,53,58", "endOffsets": "2371,101,1103,2620,725,2331,1045,844,3055,2770,2504,2958,2869,2443,182,366,304,416,240,1805,1629,2578,963,595,508,1975,2130,1171,1868,1729,2544,2658,2029,2209,657,1381,1503,1447,1563,1321,785,2263,1230"}, "to": {"startLines": "140,142,144,156,160,161,162,201,204,205,206,207,208,221,223,224,225,226,227,229,230,233,235,236,237,238,239,242,243,244,245,246,247,250,252,272,273,274,275,276,277,278,279", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8186,8309,8426,9318,9527,9595,9663,12490,12651,12748,12835,12896,12985,14305,14421,14473,14535,14599,14649,14769,14845,15001,15069,15188,15275,15338,15412,15617,15685,15748,15814,15854,15892,16045,16150,17403,17463,17519,17585,17645,17705,17765,17819", "endColumns": "39,50,57,41,67,67,53,58,96,86,60,88,98,46,51,61,63,49,57,75,65,33,118,86,62,73,100,67,62,65,39,37,53,51,61,59,55,65,59,59,59,53,58", "endOffsets": "8221,8355,8479,9355,9590,9658,9712,12544,12743,12830,12891,12980,13079,14347,14468,14530,14594,14644,14702,14840,14906,15030,15183,15270,15333,15407,15508,15680,15743,15809,15849,15887,15941,16092,16207,17458,17514,17580,17640,17700,17760,17814,17873"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/b52eefb7eefa5c6ad84a8949ac37e1b4/transformed/core-1.12.0/res/values/values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,155,176,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8840,9521,10203", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,154,175,208,214", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8835,9516,10198,10365"}, "to": {"startLines": "2,3,4,6,7,19,20,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,102,103,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,139,149,150,151,152,153,154,155,253,294,295,299,300,304,306,307,349,355,365,398,428,461", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,210,282,412,477,1133,1202,2144,2214,2282,2354,2424,2485,2559,2632,2693,2754,2816,2880,2942,3003,3071,3171,3231,3297,3370,3439,3496,3548,3610,3682,3758,6187,6222,6484,6539,6602,6657,6715,6773,6834,6897,6954,7005,7055,7116,7173,7239,7273,7308,8116,8807,8874,8946,9015,9084,9158,9230,16212,18659,18776,18977,19087,19288,19506,19578,20936,21139,21440,23171,24171,24853", "endLines": "2,3,4,6,7,19,20,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,102,103,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,139,149,150,151,152,153,154,155,253,294,298,299,303,304,306,307,354,364,397,418,460,466", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "205,277,365,472,538,1197,1260,2209,2277,2349,2419,2480,2554,2627,2688,2749,2811,2875,2937,2998,3066,3166,3226,3292,3365,3434,3491,3543,3605,3677,3753,3818,6217,6252,6534,6597,6652,6710,6768,6829,6892,6949,7000,7050,7111,7168,7234,7268,7303,7338,8181,8869,8941,9010,9079,9153,9225,9313,16278,18771,18972,19082,19283,19412,19573,19640,21134,21435,23166,23847,24848,25015"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/9aceb0555898d036bfa3b838a4051259/transformed/jetified-material3-1.1.2/res/values/values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,139,221,298,378,426,487,566,668,750,866,916,981,1038,1103,1188,1279,1349,1442,1531,1625,1770,1857,1941,2033,2127,2187,2251,2334,2424,2487,2555,2623,2720,2825,2897,2962,3006,3052,3121,3174,3227,3295,3341,3391,3458,3525,3591,3656,3710,3782,3849,3919,4001,4047,4113", "endLines": "2,3,4,5,6,7,8,9,10,13,14,15,16,17,18,19,20,21,22,23,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "endColumns": "83,81,76,79,47,60,78,101,81,13,49,64,56,64,84,90,69,92,88,93,13,86,83,91,93,59,63,82,89,62,67,67,96,104,71,64,43,45,68,52,52,67,45,49,66,66,65,64,53,71,66,69,81,45,65,60", "endOffsets": "134,216,293,373,421,482,561,663,745,861,911,976,1033,1098,1183,1274,1344,1437,1526,1620,1765,1852,1936,2028,2122,2182,2246,2329,2419,2482,2550,2618,2715,2820,2892,2957,3001,3047,3116,3169,3222,3290,3336,3386,3453,3520,3586,3651,3705,3777,3844,3914,3996,4042,4108,4169"}, "to": {"startLines": "145,146,147,148,159,163,164,165,166,167,170,171,172,173,174,175,176,177,178,179,180,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,202,209,222,248,251,254,258,259,260,261,262,263,264,265,266,267,268,269,270,271", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8484,8568,8650,8727,9479,9717,9778,9857,9959,10041,10157,10207,10272,10329,10394,10479,10570,10640,10733,10822,10916,11061,11148,11232,11324,11418,11478,11542,11625,11715,11778,11846,11914,12011,12116,12188,12549,13084,14352,15946,16097,16283,16524,16570,16620,16687,16754,16820,16885,16939,17011,17078,17148,17230,17276,17342", "endLines": "145,146,147,148,159,163,164,165,166,169,170,171,172,173,174,175,176,177,178,179,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,202,209,222,248,251,254,258,259,260,261,262,263,264,265,266,267,268,269,270,271", "endColumns": "83,81,76,79,47,60,78,101,81,13,49,64,56,64,84,90,69,92,88,93,13,86,83,91,93,59,63,82,89,62,67,67,96,104,71,64,43,45,68,52,52,67,45,49,66,66,65,64,53,71,66,69,81,45,65,60", "endOffsets": "8563,8645,8722,8802,9522,9773,9852,9954,10036,10152,10202,10267,10324,10389,10474,10565,10635,10728,10817,10911,11056,11143,11227,11319,11413,11473,11537,11620,11710,11773,11841,11909,12006,12111,12183,12248,12588,13125,14416,15994,16145,16346,16565,16615,16682,16749,16815,16880,16934,17006,17073,17143,17225,17271,17337,17398"}}]}]}