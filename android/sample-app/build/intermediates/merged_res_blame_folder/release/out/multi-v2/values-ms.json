{"logs": [{"outputFile": "com.moneta.sampleapp.sample-app-mergeReleaseResources-54:/values-ms/values-ms.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.12/transforms/b52eefb7eefa5c6ad84a8949ac37e1b4/transformed/core-1.12.0/res/values-ms/values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,349,459,565,683,798", "endColumns": "94,101,96,109,105,117,114,100", "endOffsets": "145,247,344,454,560,678,793,894"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "561,656,758,855,965,1071,1189,6481", "endColumns": "94,101,96,109,105,117,114,100", "endOffsets": "651,753,850,960,1066,1184,1299,6577"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/6dcd5d8b64257e5a867f481fb385b30c/transformed/jetified-ui-release/res/values-ms/values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,195,279,375,477,562,645,740,827,912,978,1045,1130,1216,1288,1364,1430", "endColumns": "89,83,95,101,84,82,94,86,84,65,66,84,85,71,75,65,119", "endOffsets": "190,274,370,472,557,640,735,822,907,973,1040,1125,1211,1283,1359,1425,1545"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1304,1394,5152,5248,5422,5587,5670,5765,5852,5937,6003,6070,6155,6324,6680,6756,6822", "endColumns": "89,83,95,101,84,82,94,86,84,65,66,84,85,71,75,65,119", "endOffsets": "1389,1473,5243,5345,5502,5665,5760,5847,5932,5998,6065,6150,6236,6391,6751,6817,6937"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/9aceb0555898d036bfa3b838a4051259/transformed/jetified-material3-1.1.2/res/values-ms/values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,171,286,396,511,591,692,811,957,1081,1225,1307,1403,1491,1585,1697,1819,1920,2054,2185,2313,2488,2609,2730,2855,2981,3070,3167,3284,3408,3505,3608,3710,3846,3988,4091,4185,4257,4337,4420,4505,4603,4679,4758,4853,4948,5041,5140,5223,5322,5417,5519,5637,5714,5816", "endColumns": "115,114,109,114,79,100,118,145,123,143,81,95,87,93,111,121,100,133,130,127,174,120,120,124,125,88,96,116,123,96,102,101,135,141,102,93,71,79,82,84,97,75,78,94,94,92,98,82,98,94,101,117,76,101,91", "endOffsets": "166,281,391,506,586,687,806,952,1076,1220,1302,1398,1486,1580,1692,1814,1915,2049,2180,2308,2483,2604,2725,2850,2976,3065,3162,3279,3403,3500,3603,3705,3841,3983,4086,4180,4252,4332,4415,4500,4598,4674,4753,4848,4943,5036,5135,5218,5317,5412,5514,5632,5709,5811,5903"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,221,336,446,1478,1558,1659,1778,1924,2048,2192,2274,2370,2458,2552,2664,2786,2887,3021,3152,3280,3455,3576,3697,3822,3948,4037,4134,4251,4375,4472,4575,4677,4813,4955,5058,5350,5507,6241,6396,6582,6942,7018,7097,7192,7287,7380,7479,7562,7661,7756,7858,7976,8053,8155", "endColumns": "115,114,109,114,79,100,118,145,123,143,81,95,87,93,111,121,100,133,130,127,174,120,120,124,125,88,96,116,123,96,102,101,135,141,102,93,71,79,82,84,97,75,78,94,94,92,98,82,98,94,101,117,76,101,91", "endOffsets": "216,331,441,556,1553,1654,1773,1919,2043,2187,2269,2365,2453,2547,2659,2781,2882,3016,3147,3275,3450,3571,3692,3817,3943,4032,4129,4246,4370,4467,4570,4672,4808,4950,5053,5147,5417,5582,6319,6476,6675,7013,7092,7187,7282,7375,7474,7557,7656,7751,7853,7971,8048,8150,8242"}}]}]}