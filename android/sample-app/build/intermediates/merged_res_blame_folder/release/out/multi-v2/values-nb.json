{"logs": [{"outputFile": "com.moneta.sampleapp.sample-app-mergeReleaseResources-54:/values-nb/values-nb.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.12/transforms/b52eefb7eefa5c6ad84a8949ac37e1b4/transformed/core-1.12.0/res/values-nb/values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,447,555,661,781", "endColumns": "93,101,96,98,107,105,119,100", "endOffsets": "144,246,343,442,550,656,776,877"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "541,635,737,834,933,1041,1147,6272", "endColumns": "93,101,96,98,107,105,119,100", "endOffsets": "630,732,829,928,1036,1142,1262,6368"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/6dcd5d8b64257e5a867f481fb385b30c/transformed/jetified-ui-release/res/values-nb/values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,279,376,476,564,640,728,817,899,963,1027,1107,1189,1259,1336,1403", "endColumns": "92,80,96,99,87,75,87,88,81,63,63,79,81,69,76,66,119", "endOffsets": "193,274,371,471,559,635,723,812,894,958,1022,1102,1184,1254,1331,1398,1518"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1267,1360,4983,5080,5256,5417,5493,5581,5670,5752,5816,5880,5960,6122,6480,6557,6624", "endColumns": "92,80,96,99,87,75,87,88,81,63,63,79,81,69,76,66,119", "endOffsets": "1355,1436,5075,5175,5339,5488,5576,5665,5747,5811,5875,5955,6037,6187,6552,6619,6739"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/9aceb0555898d036bfa3b838a4051259/transformed/jetified-material3-1.1.2/res/values-nb/values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,168,279,382,491,566,656,764,904,1022,1168,1248,1344,1429,1519,1622,1736,1837,1960,2078,2207,2370,2493,2606,2725,2843,2929,3023,3138,3271,3368,3474,3573,3702,3839,3940,4033,4109,4182,4262,4342,4449,4525,4605,4702,4797,4884,4980,5064,5165,5263,5363,5476,5552,5651", "endColumns": "112,110,102,108,74,89,107,139,117,145,79,95,84,89,102,113,100,122,117,128,162,122,112,118,117,85,93,114,132,96,105,98,128,136,100,92,75,72,79,79,106,75,79,96,94,86,95,83,100,97,99,112,75,98,94", "endOffsets": "163,274,377,486,561,651,759,899,1017,1163,1243,1339,1424,1514,1617,1731,1832,1955,2073,2202,2365,2488,2601,2720,2838,2924,3018,3133,3266,3363,3469,3568,3697,3834,3935,4028,4104,4177,4257,4337,4444,4520,4600,4697,4792,4879,4975,5059,5160,5258,5358,5471,5547,5646,5741"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,218,329,432,1441,1516,1606,1714,1854,1972,2118,2198,2294,2379,2469,2572,2686,2787,2910,3028,3157,3320,3443,3556,3675,3793,3879,3973,4088,4221,4318,4424,4523,4652,4789,4890,5180,5344,6042,6192,6373,6744,6820,6900,6997,7092,7179,7275,7359,7460,7558,7658,7771,7847,7946", "endColumns": "112,110,102,108,74,89,107,139,117,145,79,95,84,89,102,113,100,122,117,128,162,122,112,118,117,85,93,114,132,96,105,98,128,136,100,92,75,72,79,79,106,75,79,96,94,86,95,83,100,97,99,112,75,98,94", "endOffsets": "213,324,427,536,1511,1601,1709,1849,1967,2113,2193,2289,2374,2464,2567,2681,2782,2905,3023,3152,3315,3438,3551,3670,3788,3874,3968,4083,4216,4313,4419,4518,4647,4784,4885,4978,5251,5412,6117,6267,6475,6815,6895,6992,7087,7174,7270,7354,7455,7553,7653,7766,7842,7941,8036"}}]}]}