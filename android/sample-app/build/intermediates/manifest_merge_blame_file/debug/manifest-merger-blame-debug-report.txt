1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.moneta.sampleapp"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="34" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/AndroidManifest.xml:5:5-67
11-->/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/AndroidManifest.xml:5:22-64
12    <uses-permission android:name="android.permission.CAMERA" />
12-->/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/AndroidManifest.xml:6:5-65
12-->/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/AndroidManifest.xml:6:22-62
13
14    <uses-feature
14-->/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/AndroidManifest.xml:8:5-10:36
15        android:name="android.hardware.camera"
15-->/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/AndroidManifest.xml:9:9-47
16        android:required="false" />
16-->/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/AndroidManifest.xml:10:9-33
17
18    <permission
18-->[androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.12/transforms/b52eefb7eefa5c6ad84a8949ac37e1b4/transformed/core-1.12.0/AndroidManifest.xml:22:5-24:47
19        android:name="com.moneta.sampleapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
19-->[androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.12/transforms/b52eefb7eefa5c6ad84a8949ac37e1b4/transformed/core-1.12.0/AndroidManifest.xml:23:9-81
20        android:protectionLevel="signature" />
20-->[androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.12/transforms/b52eefb7eefa5c6ad84a8949ac37e1b4/transformed/core-1.12.0/AndroidManifest.xml:24:9-44
21
22    <uses-permission android:name="com.moneta.sampleapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
22-->[androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.12/transforms/b52eefb7eefa5c6ad84a8949ac37e1b4/transformed/core-1.12.0/AndroidManifest.xml:26:5-97
22-->[androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.12/transforms/b52eefb7eefa5c6ad84a8949ac37e1b4/transformed/core-1.12.0/AndroidManifest.xml:26:22-94
23
24    <uses-feature
24-->[com.journeyapps:zxing-android-embedded:4.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/93bc7b62ed7d90b46adb14d1a6562db2/transformed/jetified-zxing-android-embedded-4.3.0/AndroidManifest.xml:28:5-30:36
25        android:name="android.hardware.camera.front"
25-->[com.journeyapps:zxing-android-embedded:4.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/93bc7b62ed7d90b46adb14d1a6562db2/transformed/jetified-zxing-android-embedded-4.3.0/AndroidManifest.xml:29:9-53
26        android:required="false" /> <!-- TODO replace above two with next line after Android 4.2 -->
26-->[com.journeyapps:zxing-android-embedded:4.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/93bc7b62ed7d90b46adb14d1a6562db2/transformed/jetified-zxing-android-embedded-4.3.0/AndroidManifest.xml:30:9-33
27    <!-- <uses-feature android:name="android.hardware.camera.any"/> -->
28    <uses-feature
28-->[com.journeyapps:zxing-android-embedded:4.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/93bc7b62ed7d90b46adb14d1a6562db2/transformed/jetified-zxing-android-embedded-4.3.0/AndroidManifest.xml:33:5-35:36
29        android:name="android.hardware.camera.autofocus"
29-->[com.journeyapps:zxing-android-embedded:4.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/93bc7b62ed7d90b46adb14d1a6562db2/transformed/jetified-zxing-android-embedded-4.3.0/AndroidManifest.xml:34:9-57
30        android:required="false" />
30-->[com.journeyapps:zxing-android-embedded:4.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/93bc7b62ed7d90b46adb14d1a6562db2/transformed/jetified-zxing-android-embedded-4.3.0/AndroidManifest.xml:35:9-33
31    <uses-feature
31-->[com.journeyapps:zxing-android-embedded:4.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/93bc7b62ed7d90b46adb14d1a6562db2/transformed/jetified-zxing-android-embedded-4.3.0/AndroidManifest.xml:36:5-38:36
32        android:name="android.hardware.camera.flash"
32-->[com.journeyapps:zxing-android-embedded:4.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/93bc7b62ed7d90b46adb14d1a6562db2/transformed/jetified-zxing-android-embedded-4.3.0/AndroidManifest.xml:37:9-53
33        android:required="false" />
33-->[com.journeyapps:zxing-android-embedded:4.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/93bc7b62ed7d90b46adb14d1a6562db2/transformed/jetified-zxing-android-embedded-4.3.0/AndroidManifest.xml:38:9-33
34    <uses-feature
34-->[com.journeyapps:zxing-android-embedded:4.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/93bc7b62ed7d90b46adb14d1a6562db2/transformed/jetified-zxing-android-embedded-4.3.0/AndroidManifest.xml:39:5-41:36
35        android:name="android.hardware.screen.landscape"
35-->[com.journeyapps:zxing-android-embedded:4.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/93bc7b62ed7d90b46adb14d1a6562db2/transformed/jetified-zxing-android-embedded-4.3.0/AndroidManifest.xml:40:9-57
36        android:required="false" />
36-->[com.journeyapps:zxing-android-embedded:4.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/93bc7b62ed7d90b46adb14d1a6562db2/transformed/jetified-zxing-android-embedded-4.3.0/AndroidManifest.xml:41:9-33
37    <uses-feature
37-->[com.journeyapps:zxing-android-embedded:4.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/93bc7b62ed7d90b46adb14d1a6562db2/transformed/jetified-zxing-android-embedded-4.3.0/AndroidManifest.xml:42:5-44:36
38        android:name="android.hardware.wifi"
38-->[com.journeyapps:zxing-android-embedded:4.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/93bc7b62ed7d90b46adb14d1a6562db2/transformed/jetified-zxing-android-embedded-4.3.0/AndroidManifest.xml:43:9-45
39        android:required="false" />
39-->[com.journeyapps:zxing-android-embedded:4.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/93bc7b62ed7d90b46adb14d1a6562db2/transformed/jetified-zxing-android-embedded-4.3.0/AndroidManifest.xml:44:9-33
40
41    <application
41-->/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/AndroidManifest.xml:12:5-40:19
42        android:name="com.moneta.sampleapp.MonetaSampleApplication"
42-->/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/AndroidManifest.xml:13:9-48
43        android:allowBackup="true"
43-->/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/AndroidManifest.xml:14:9-35
44        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
44-->[androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.12/transforms/b52eefb7eefa5c6ad84a8949ac37e1b4/transformed/core-1.12.0/AndroidManifest.xml:28:18-86
45        android:dataExtractionRules="@xml/data_extraction_rules"
45-->/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/AndroidManifest.xml:15:9-65
46        android:debuggable="true"
47        android:extractNativeLibs="true"
48        android:fullBackupContent="@xml/backup_rules"
48-->/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/AndroidManifest.xml:16:9-54
49        android:icon="@mipmap/ic_launcher"
49-->/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/AndroidManifest.xml:17:9-43
50        android:label="@string/app_name"
50-->/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/AndroidManifest.xml:18:9-41
51        android:roundIcon="@mipmap/ic_launcher_round"
51-->/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/AndroidManifest.xml:19:9-54
52        android:supportsRtl="true"
52-->/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/AndroidManifest.xml:20:9-35
53        android:theme="@style/Theme.MonetaSample" >
53-->/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/AndroidManifest.xml:21:9-50
54        <activity
54-->/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/AndroidManifest.xml:24:9-32:20
55            android:name="com.moneta.sampleapp.MainActivity"
55-->/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/AndroidManifest.xml:25:13-41
56            android:exported="true"
56-->/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/AndroidManifest.xml:26:13-36
57            android:theme="@style/Theme.MonetaSample" >
57-->/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/AndroidManifest.xml:27:13-54
58            <intent-filter>
58-->/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/AndroidManifest.xml:28:13-31:29
59                <action android:name="android.intent.action.MAIN" />
59-->/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/AndroidManifest.xml:29:17-69
59-->/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/AndroidManifest.xml:29:25-66
60
61                <category android:name="android.intent.category.LAUNCHER" />
61-->/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/AndroidManifest.xml:30:17-77
61-->/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/AndroidManifest.xml:30:27-74
62            </intent-filter>
63        </activity>
64
65        <!-- QR Code Scanner Activity -->
66        <activity
66-->/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/AndroidManifest.xml:35:9-38:49
67            android:name="com.journeyapps.barcodescanner.CaptureActivity"
67-->/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/AndroidManifest.xml:36:13-74
68            android:clearTaskOnLaunch="true"
68-->[com.journeyapps:zxing-android-embedded:4.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/93bc7b62ed7d90b46adb14d1a6562db2/transformed/jetified-zxing-android-embedded-4.3.0/AndroidManifest.xml:49:13-45
69            android:screenOrientation="fullSensor"
69-->/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/AndroidManifest.xml:37:13-51
70            android:stateNotNeeded="true"
70-->[com.journeyapps:zxing-android-embedded:4.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/93bc7b62ed7d90b46adb14d1a6562db2/transformed/jetified-zxing-android-embedded-4.3.0/AndroidManifest.xml:51:13-42
71            android:theme="@style/zxing_CaptureTheme"
71-->[com.journeyapps:zxing-android-embedded:4.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/93bc7b62ed7d90b46adb14d1a6562db2/transformed/jetified-zxing-android-embedded-4.3.0/AndroidManifest.xml:52:13-54
72            android:windowSoftInputMode="stateAlwaysHidden" />
72-->[com.journeyapps:zxing-android-embedded:4.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/93bc7b62ed7d90b46adb14d1a6562db2/transformed/jetified-zxing-android-embedded-4.3.0/AndroidManifest.xml:53:13-60
73
74        <provider
74-->[androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/b4374047bc445910aa5fc28ce452a90c/transformed/jetified-emoji2-1.4.0/AndroidManifest.xml:24:9-32:20
75            android:name="androidx.startup.InitializationProvider"
75-->[androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/b4374047bc445910aa5fc28ce452a90c/transformed/jetified-emoji2-1.4.0/AndroidManifest.xml:25:13-67
76            android:authorities="com.moneta.sampleapp.androidx-startup"
76-->[androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/b4374047bc445910aa5fc28ce452a90c/transformed/jetified-emoji2-1.4.0/AndroidManifest.xml:26:13-68
77            android:exported="false" >
77-->[androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/b4374047bc445910aa5fc28ce452a90c/transformed/jetified-emoji2-1.4.0/AndroidManifest.xml:27:13-37
78            <meta-data
78-->[androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/b4374047bc445910aa5fc28ce452a90c/transformed/jetified-emoji2-1.4.0/AndroidManifest.xml:29:13-31:52
79                android:name="androidx.emoji2.text.EmojiCompatInitializer"
79-->[androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/b4374047bc445910aa5fc28ce452a90c/transformed/jetified-emoji2-1.4.0/AndroidManifest.xml:30:17-75
80                android:value="androidx.startup" />
80-->[androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/b4374047bc445910aa5fc28ce452a90c/transformed/jetified-emoji2-1.4.0/AndroidManifest.xml:31:17-49
81            <meta-data
81-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/54692633a1b1700654ac536ed9f93313/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:29:13-31:52
82                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
82-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/54692633a1b1700654ac536ed9f93313/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:30:17-78
83                android:value="androidx.startup" />
83-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/54692633a1b1700654ac536ed9f93313/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:31:17-49
84            <meta-data
84-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/3fbcbafc1e60471e08c002f3f605bdd0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:29:13-31:52
85                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
85-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/3fbcbafc1e60471e08c002f3f605bdd0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:30:17-85
86                android:value="androidx.startup" />
86-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/3fbcbafc1e60471e08c002f3f605bdd0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:31:17-49
87        </provider>
88
89        <activity
89-->[androidx.compose.ui:ui-test-manifest:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/ae37421d883d5bbdb1243f2232d1d420/transformed/jetified-ui-test-manifest-1.5.4/AndroidManifest.xml:23:9-25:39
90            android:name="androidx.activity.ComponentActivity"
90-->[androidx.compose.ui:ui-test-manifest:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/ae37421d883d5bbdb1243f2232d1d420/transformed/jetified-ui-test-manifest-1.5.4/AndroidManifest.xml:24:13-63
91            android:exported="true" />
91-->[androidx.compose.ui:ui-test-manifest:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/ae37421d883d5bbdb1243f2232d1d420/transformed/jetified-ui-test-manifest-1.5.4/AndroidManifest.xml:25:13-36
92        <activity
92-->[androidx.compose.ui:ui-tooling-android:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/b5417efa0dd24d59af9512bb01944f60/transformed/jetified-ui-tooling-release/AndroidManifest.xml:23:9-25:39
93            android:name="androidx.compose.ui.tooling.PreviewActivity"
93-->[androidx.compose.ui:ui-tooling-android:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/b5417efa0dd24d59af9512bb01944f60/transformed/jetified-ui-tooling-release/AndroidManifest.xml:24:13-71
94            android:exported="true" />
94-->[androidx.compose.ui:ui-tooling-android:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/b5417efa0dd24d59af9512bb01944f60/transformed/jetified-ui-tooling-release/AndroidManifest.xml:25:13-36
95
96        <receiver
96-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/3fbcbafc1e60471e08c002f3f605bdd0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:34:9-52:20
97            android:name="androidx.profileinstaller.ProfileInstallReceiver"
97-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/3fbcbafc1e60471e08c002f3f605bdd0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:35:13-76
98            android:directBootAware="false"
98-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/3fbcbafc1e60471e08c002f3f605bdd0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:36:13-44
99            android:enabled="true"
99-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/3fbcbafc1e60471e08c002f3f605bdd0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:37:13-35
100            android:exported="true"
100-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/3fbcbafc1e60471e08c002f3f605bdd0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:38:13-36
101            android:permission="android.permission.DUMP" >
101-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/3fbcbafc1e60471e08c002f3f605bdd0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:39:13-57
102            <intent-filter>
102-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/3fbcbafc1e60471e08c002f3f605bdd0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:40:13-42:29
103                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
103-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/3fbcbafc1e60471e08c002f3f605bdd0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:41:17-91
103-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/3fbcbafc1e60471e08c002f3f605bdd0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:41:25-88
104            </intent-filter>
105            <intent-filter>
105-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/3fbcbafc1e60471e08c002f3f605bdd0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:43:13-45:29
106                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
106-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/3fbcbafc1e60471e08c002f3f605bdd0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:44:17-85
106-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/3fbcbafc1e60471e08c002f3f605bdd0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:44:25-82
107            </intent-filter>
108            <intent-filter>
108-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/3fbcbafc1e60471e08c002f3f605bdd0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:46:13-48:29
109                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
109-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/3fbcbafc1e60471e08c002f3f605bdd0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:47:17-88
109-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/3fbcbafc1e60471e08c002f3f605bdd0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:47:25-85
110            </intent-filter>
111            <intent-filter>
111-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/3fbcbafc1e60471e08c002f3f605bdd0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:49:13-51:29
112                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
112-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/3fbcbafc1e60471e08c002f3f605bdd0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:50:17-95
112-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/3fbcbafc1e60471e08c002f3f605bdd0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:50:25-92
113            </intent-filter>
114        </receiver>
115    </application>
116
117</manifest>
