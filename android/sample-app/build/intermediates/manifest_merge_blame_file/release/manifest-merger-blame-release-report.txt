1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.moneta.sampleapp"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="34" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/AndroidManifest.xml:5:5-67
11-->/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/AndroidManifest.xml:5:22-64
12    <uses-permission android:name="android.permission.CAMERA" />
12-->/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/AndroidManifest.xml:6:5-65
12-->/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/AndroidManifest.xml:6:22-62
13
14    <uses-feature
14-->/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/AndroidManifest.xml:8:5-10:36
15        android:name="android.hardware.camera"
15-->/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/AndroidManifest.xml:9:9-47
16        android:required="false" />
16-->/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/AndroidManifest.xml:10:9-33
17
18    <permission
18-->[androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.12/transforms/b52eefb7eefa5c6ad84a8949ac37e1b4/transformed/core-1.12.0/AndroidManifest.xml:22:5-24:47
19        android:name="com.moneta.sampleapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
19-->[androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.12/transforms/b52eefb7eefa5c6ad84a8949ac37e1b4/transformed/core-1.12.0/AndroidManifest.xml:23:9-81
20        android:protectionLevel="signature" />
20-->[androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.12/transforms/b52eefb7eefa5c6ad84a8949ac37e1b4/transformed/core-1.12.0/AndroidManifest.xml:24:9-44
21
22    <uses-permission android:name="com.moneta.sampleapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
22-->[androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.12/transforms/b52eefb7eefa5c6ad84a8949ac37e1b4/transformed/core-1.12.0/AndroidManifest.xml:26:5-97
22-->[androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.12/transforms/b52eefb7eefa5c6ad84a8949ac37e1b4/transformed/core-1.12.0/AndroidManifest.xml:26:22-94
23
24    <uses-feature
24-->[com.journeyapps:zxing-android-embedded:4.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/93bc7b62ed7d90b46adb14d1a6562db2/transformed/jetified-zxing-android-embedded-4.3.0/AndroidManifest.xml:28:5-30:36
25        android:name="android.hardware.camera.front"
25-->[com.journeyapps:zxing-android-embedded:4.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/93bc7b62ed7d90b46adb14d1a6562db2/transformed/jetified-zxing-android-embedded-4.3.0/AndroidManifest.xml:29:9-53
26        android:required="false" /> <!-- TODO replace above two with next line after Android 4.2 -->
26-->[com.journeyapps:zxing-android-embedded:4.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/93bc7b62ed7d90b46adb14d1a6562db2/transformed/jetified-zxing-android-embedded-4.3.0/AndroidManifest.xml:30:9-33
27    <!-- <uses-feature android:name="android.hardware.camera.any"/> -->
28    <uses-feature
28-->[com.journeyapps:zxing-android-embedded:4.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/93bc7b62ed7d90b46adb14d1a6562db2/transformed/jetified-zxing-android-embedded-4.3.0/AndroidManifest.xml:33:5-35:36
29        android:name="android.hardware.camera.autofocus"
29-->[com.journeyapps:zxing-android-embedded:4.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/93bc7b62ed7d90b46adb14d1a6562db2/transformed/jetified-zxing-android-embedded-4.3.0/AndroidManifest.xml:34:9-57
30        android:required="false" />
30-->[com.journeyapps:zxing-android-embedded:4.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/93bc7b62ed7d90b46adb14d1a6562db2/transformed/jetified-zxing-android-embedded-4.3.0/AndroidManifest.xml:35:9-33
31    <uses-feature
31-->[com.journeyapps:zxing-android-embedded:4.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/93bc7b62ed7d90b46adb14d1a6562db2/transformed/jetified-zxing-android-embedded-4.3.0/AndroidManifest.xml:36:5-38:36
32        android:name="android.hardware.camera.flash"
32-->[com.journeyapps:zxing-android-embedded:4.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/93bc7b62ed7d90b46adb14d1a6562db2/transformed/jetified-zxing-android-embedded-4.3.0/AndroidManifest.xml:37:9-53
33        android:required="false" />
33-->[com.journeyapps:zxing-android-embedded:4.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/93bc7b62ed7d90b46adb14d1a6562db2/transformed/jetified-zxing-android-embedded-4.3.0/AndroidManifest.xml:38:9-33
34    <uses-feature
34-->[com.journeyapps:zxing-android-embedded:4.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/93bc7b62ed7d90b46adb14d1a6562db2/transformed/jetified-zxing-android-embedded-4.3.0/AndroidManifest.xml:39:5-41:36
35        android:name="android.hardware.screen.landscape"
35-->[com.journeyapps:zxing-android-embedded:4.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/93bc7b62ed7d90b46adb14d1a6562db2/transformed/jetified-zxing-android-embedded-4.3.0/AndroidManifest.xml:40:9-57
36        android:required="false" />
36-->[com.journeyapps:zxing-android-embedded:4.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/93bc7b62ed7d90b46adb14d1a6562db2/transformed/jetified-zxing-android-embedded-4.3.0/AndroidManifest.xml:41:9-33
37    <uses-feature
37-->[com.journeyapps:zxing-android-embedded:4.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/93bc7b62ed7d90b46adb14d1a6562db2/transformed/jetified-zxing-android-embedded-4.3.0/AndroidManifest.xml:42:5-44:36
38        android:name="android.hardware.wifi"
38-->[com.journeyapps:zxing-android-embedded:4.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/93bc7b62ed7d90b46adb14d1a6562db2/transformed/jetified-zxing-android-embedded-4.3.0/AndroidManifest.xml:43:9-45
39        android:required="false" />
39-->[com.journeyapps:zxing-android-embedded:4.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/93bc7b62ed7d90b46adb14d1a6562db2/transformed/jetified-zxing-android-embedded-4.3.0/AndroidManifest.xml:44:9-33
40
41    <application
41-->/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/AndroidManifest.xml:12:5-40:19
42        android:name="com.moneta.sampleapp.MonetaSampleApplication"
42-->/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/AndroidManifest.xml:13:9-48
43        android:allowBackup="true"
43-->/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/AndroidManifest.xml:14:9-35
44        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
44-->[androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.12/transforms/b52eefb7eefa5c6ad84a8949ac37e1b4/transformed/core-1.12.0/AndroidManifest.xml:28:18-86
45        android:dataExtractionRules="@xml/data_extraction_rules"
45-->/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/AndroidManifest.xml:15:9-65
46        android:extractNativeLibs="true"
47        android:fullBackupContent="@xml/backup_rules"
47-->/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/AndroidManifest.xml:16:9-54
48        android:icon="@mipmap/ic_launcher"
48-->/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/AndroidManifest.xml:17:9-43
49        android:label="@string/app_name"
49-->/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/AndroidManifest.xml:18:9-41
50        android:roundIcon="@mipmap/ic_launcher_round"
50-->/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/AndroidManifest.xml:19:9-54
51        android:supportsRtl="true"
51-->/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/AndroidManifest.xml:20:9-35
52        android:theme="@style/Theme.MonetaSample" >
52-->/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/AndroidManifest.xml:21:9-50
53        <activity
53-->/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/AndroidManifest.xml:24:9-32:20
54            android:name="com.moneta.sampleapp.MainActivity"
54-->/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/AndroidManifest.xml:25:13-41
55            android:exported="true"
55-->/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/AndroidManifest.xml:26:13-36
56            android:theme="@style/Theme.MonetaSample" >
56-->/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/AndroidManifest.xml:27:13-54
57            <intent-filter>
57-->/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/AndroidManifest.xml:28:13-31:29
58                <action android:name="android.intent.action.MAIN" />
58-->/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/AndroidManifest.xml:29:17-69
58-->/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/AndroidManifest.xml:29:25-66
59
60                <category android:name="android.intent.category.LAUNCHER" />
60-->/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/AndroidManifest.xml:30:17-77
60-->/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/AndroidManifest.xml:30:27-74
61            </intent-filter>
62        </activity>
63
64        <!-- QR Code Scanner Activity -->
65        <activity
65-->/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/AndroidManifest.xml:35:9-38:49
66            android:name="com.journeyapps.barcodescanner.CaptureActivity"
66-->/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/AndroidManifest.xml:36:13-74
67            android:clearTaskOnLaunch="true"
67-->[com.journeyapps:zxing-android-embedded:4.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/93bc7b62ed7d90b46adb14d1a6562db2/transformed/jetified-zxing-android-embedded-4.3.0/AndroidManifest.xml:49:13-45
68            android:screenOrientation="fullSensor"
68-->/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sample-app/src/main/AndroidManifest.xml:37:13-51
69            android:stateNotNeeded="true"
69-->[com.journeyapps:zxing-android-embedded:4.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/93bc7b62ed7d90b46adb14d1a6562db2/transformed/jetified-zxing-android-embedded-4.3.0/AndroidManifest.xml:51:13-42
70            android:theme="@style/zxing_CaptureTheme"
70-->[com.journeyapps:zxing-android-embedded:4.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/93bc7b62ed7d90b46adb14d1a6562db2/transformed/jetified-zxing-android-embedded-4.3.0/AndroidManifest.xml:52:13-54
71            android:windowSoftInputMode="stateAlwaysHidden" />
71-->[com.journeyapps:zxing-android-embedded:4.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/93bc7b62ed7d90b46adb14d1a6562db2/transformed/jetified-zxing-android-embedded-4.3.0/AndroidManifest.xml:53:13-60
72
73        <provider
73-->[androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/b4374047bc445910aa5fc28ce452a90c/transformed/jetified-emoji2-1.4.0/AndroidManifest.xml:24:9-32:20
74            android:name="androidx.startup.InitializationProvider"
74-->[androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/b4374047bc445910aa5fc28ce452a90c/transformed/jetified-emoji2-1.4.0/AndroidManifest.xml:25:13-67
75            android:authorities="com.moneta.sampleapp.androidx-startup"
75-->[androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/b4374047bc445910aa5fc28ce452a90c/transformed/jetified-emoji2-1.4.0/AndroidManifest.xml:26:13-68
76            android:exported="false" >
76-->[androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/b4374047bc445910aa5fc28ce452a90c/transformed/jetified-emoji2-1.4.0/AndroidManifest.xml:27:13-37
77            <meta-data
77-->[androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/b4374047bc445910aa5fc28ce452a90c/transformed/jetified-emoji2-1.4.0/AndroidManifest.xml:29:13-31:52
78                android:name="androidx.emoji2.text.EmojiCompatInitializer"
78-->[androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/b4374047bc445910aa5fc28ce452a90c/transformed/jetified-emoji2-1.4.0/AndroidManifest.xml:30:17-75
79                android:value="androidx.startup" />
79-->[androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/b4374047bc445910aa5fc28ce452a90c/transformed/jetified-emoji2-1.4.0/AndroidManifest.xml:31:17-49
80            <meta-data
80-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/54692633a1b1700654ac536ed9f93313/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:29:13-31:52
81                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
81-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/54692633a1b1700654ac536ed9f93313/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:30:17-78
82                android:value="androidx.startup" />
82-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/54692633a1b1700654ac536ed9f93313/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:31:17-49
83            <meta-data
83-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/3fbcbafc1e60471e08c002f3f605bdd0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:29:13-31:52
84                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
84-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/3fbcbafc1e60471e08c002f3f605bdd0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:30:17-85
85                android:value="androidx.startup" />
85-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/3fbcbafc1e60471e08c002f3f605bdd0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:31:17-49
86        </provider>
87
88        <receiver
88-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/3fbcbafc1e60471e08c002f3f605bdd0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:34:9-52:20
89            android:name="androidx.profileinstaller.ProfileInstallReceiver"
89-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/3fbcbafc1e60471e08c002f3f605bdd0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:35:13-76
90            android:directBootAware="false"
90-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/3fbcbafc1e60471e08c002f3f605bdd0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:36:13-44
91            android:enabled="true"
91-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/3fbcbafc1e60471e08c002f3f605bdd0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:37:13-35
92            android:exported="true"
92-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/3fbcbafc1e60471e08c002f3f605bdd0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:38:13-36
93            android:permission="android.permission.DUMP" >
93-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/3fbcbafc1e60471e08c002f3f605bdd0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:39:13-57
94            <intent-filter>
94-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/3fbcbafc1e60471e08c002f3f605bdd0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:40:13-42:29
95                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
95-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/3fbcbafc1e60471e08c002f3f605bdd0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:41:17-91
95-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/3fbcbafc1e60471e08c002f3f605bdd0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:41:25-88
96            </intent-filter>
97            <intent-filter>
97-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/3fbcbafc1e60471e08c002f3f605bdd0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:43:13-45:29
98                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
98-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/3fbcbafc1e60471e08c002f3f605bdd0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:44:17-85
98-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/3fbcbafc1e60471e08c002f3f605bdd0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:44:25-82
99            </intent-filter>
100            <intent-filter>
100-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/3fbcbafc1e60471e08c002f3f605bdd0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:46:13-48:29
101                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
101-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/3fbcbafc1e60471e08c002f3f605bdd0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:47:17-88
101-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/3fbcbafc1e60471e08c002f3f605bdd0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:47:25-85
102            </intent-filter>
103            <intent-filter>
103-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/3fbcbafc1e60471e08c002f3f605bdd0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:49:13-51:29
104                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
104-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/3fbcbafc1e60471e08c002f3f605bdd0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:50:17-95
104-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/3fbcbafc1e60471e08c002f3f605bdd0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:50:25-92
105            </intent-filter>
106        </receiver>
107    </application>
108
109</manifest>
