<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="black">#FF000000</color>
    <color name="moneta_background">#FFFFFFFF</color>
    <color name="moneta_error">#FFB00020</color>
    <color name="moneta_on_background">#FF000000</color>
    <color name="moneta_on_error">#FFFFFFFF</color>
    <color name="moneta_on_primary">#FFFFFFFF</color>
    <color name="moneta_on_secondary">#FF000000</color>
    <color name="moneta_on_surface">#FF000000</color>
    <color name="moneta_primary">#FF1976D2</color>
    <color name="moneta_primary_variant">#FF1565C0</color>
    <color name="moneta_secondary">#FF03DAC6</color>
    <color name="moneta_surface">#FFFFFFFF</color>
    <color name="purple_200">#FFBB86FC</color>
    <color name="purple_500">#FF6200EE</color>
    <color name="purple_700">#FF3700B3</color>
    <color name="teal_200">#FF03DAC5</color>
    <color name="teal_700">#FF018786</color>
    <color name="white">#FFFFFFFF</color>
    <string name="about">About</string>
    <string name="app_name">Moneta Sample</string>
    <string name="balance_label">Current Balance</string>
    <string name="cancel">Cancel</string>
    <string name="complete_onboarding">Complete Onboarding</string>
    <string name="content_preferences">Content Preferences</string>
    <string name="dashboard_title">Dashboard</string>
    <string name="device_code_label">Device Code:</string>
    <string name="error_authentication_failed">Authentication failed. Please try again.</string>
    <string name="error_network">Network error. Please check your connection.</string>
    <string name="error_occurred">An error occurred</string>
    <string name="error_onboarding_failed">Onboarding failed. Please try again.</string>
    <string name="error_sdk_not_initialized">SDK not initialized. Please restart the app.</string>
    <string name="loading">Loading...</string>
    <string name="nav_dashboard">Dashboard</string>
    <string name="nav_publisher_auth">Publisher Auth</string>
    <string name="nav_recommendations">Recommendations</string>
    <string name="nav_settings">Settings</string>
    <string name="nav_transactions">Transactions</string>
    <string name="no_recommendations">No recommendations available</string>
    <string name="no_transactions">No transactions found</string>
    <string name="ok">OK</string>
    <string name="onboarding_instructions">Please use this code to complete your onboarding on the web portal</string>
    <string name="onboarding_subtitle">Get started by setting up your account</string>
    <string name="onboarding_title">Welcome to Moneta</string>
    <string name="publisher_auth_title">Publisher Authentication</string>
    <string name="qr_code_instructions">Scan the QR code from the publisher to authenticate</string>
    <string name="recent_transactions">Recent Transactions</string>
    <string name="recommendation_source">Source: %1$s</string>
    <string name="recommendations_title">Recommendations</string>
    <string name="retry">Retry</string>
    <string name="save">Save</string>
    <string name="scan_qr_code">Scan QR Code</string>
    <string name="settings_title">Settings</string>
    <string name="start_onboarding">Start Onboarding</string>
    <string name="transaction_amount">Amount: %1$s</string>
    <string name="transaction_date">Date: %1$s</string>
    <string name="transaction_publisher">Publisher: %1$s</string>
    <string name="transaction_status">Status: %1$s</string>
    <string name="transactions_title">Transactions</string>
    <string name="user_code_label">Your User Code:</string>
    <string name="user_profile">User Profile</string>
    <string name="view_all_transactions">View All</string>
    <style name="Theme.MonetaSample" parent="android:Theme.Material.Light.NoActionBar"/>
</resources>