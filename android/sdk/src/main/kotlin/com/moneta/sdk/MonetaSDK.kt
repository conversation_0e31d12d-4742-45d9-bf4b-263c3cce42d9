package com.moneta.sdk

import android.content.Context
import com.moneta.sdk.internal.InternalMembershipOrgClient
import com.moneta.sdk.internal.InternalMonetaCoreClient
import com.moneta.sdk.internal.InternalUAClient
import com.moneta.sdk.internal.MembershipOrgSignatureInterceptor
import com.moneta.sdk.internal.SecureStorage
import com.moneta.sdk.internal.InternalNetworkClient
import com.moneta.sdk.model.*
import com.moneta.sdk.util.MonetaException
import com.moneta.sdk.util.Result
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import com.moneta.sdk.util.Result as MonetaResult
import kotlinx.serialization.json.Json
import okhttp3.OkHttpClient
import java.security.KeyPair
import java.security.KeyPairGenerator
import java.security.SecureRandom
import java.util.Base64

class MonetaSDK private constructor() {
    companion object {
        @JvmStatic
        val shared = MonetaSDK()

        // Legacy static methods for backward compatibility
        suspend fun initialize(context: Context, baseUrl: String) {
            shared.initialize(context, baseUrl)
        }

        @JvmStatic
        val instance: MonetaSDK get() = shared
    }

    private var baseUrl: String? = null
    private var context: Context? = null
    private var secureStorage: SecureStorage? = null
    private var okHttpClient: OkHttpClient? = null
    private var jsonSerializer: Json? = null
    private var isFullyInitialized: Boolean = false

    // Internal clients
    private var membershipOrgClient: InternalMembershipOrgClient? = null
    private var monetaCoreClient: InternalMonetaCoreClient? = null
    private var uaClient: InternalUAClient? = null

    // Storage keys
    private val moInfoCurrencyKey = "mo_info_currency"
    private val moInfoRegionKey = "mo_info_region"
    private val initializationStateKey = "sdk_initialization_state"

    suspend fun initialize(context: Context, baseUrl: String) {
        if (this.baseUrl != null) {
            // Already initialized
            return
        }

        // Validate base URL
        validateBaseUrl(baseUrl)

        this.context = context.applicationContext
        this.baseUrl = baseUrl
        this.secureStorage = SecureStorage(this.context!!)

        // Initialize JSON serializer
        this.jsonSerializer = Json {
            ignoreUnknownKeys = true
            isLenient = true
        }

        // Initialize OkHttpClient with interceptors
        val signatureInterceptor = MembershipOrgSignatureInterceptor(secureStorage!!)
        this.okHttpClient = OkHttpClient.Builder()
            .addInterceptor(signatureInterceptor)
            .build()

        // Initialize internal clients with appropriate base URLs
        val networkClient = InternalNetworkClient(okHttpClient!!, jsonSerializer!!)

        membershipOrgClient = InternalMembershipOrgClient(
            baseUrl = "$baseUrl/api/mo",
            networkClient = networkClient
        )

        monetaCoreClient = InternalMonetaCoreClient(
            baseUrl = "$baseUrl/users",
            networkClient = networkClient
        )

        uaClient = InternalUAClient(
            baseUrl = "$baseUrl/recommendations",
            networkClient = networkClient
        )

        // Perform fetchMoInfo and save data to complete initialization
        performInitialMoInfoFetch()
    }
    
    private fun checkInitialized() {
        if (baseUrl == null || membershipOrgClient == null || monetaCoreClient == null || uaClient == null || !isFullyInitialized) {
            throw MonetaException.NotInitializedException()
        }
    }

    // URL Validation
    private fun validateBaseUrl(url: String) {
        try {
            val uri = java.net.URI(url)
            if (uri.scheme == null || (!uri.scheme.equals("http", ignoreCase = true) && !uri.scheme.equals("https", ignoreCase = true))) {
                throw MonetaException.ApiException(400, "Invalid base URL format. Must be a valid HTTP or HTTPS URL.")
            }
            if (uri.host == null) {
                throw MonetaException.ApiException(400, "Invalid base URL format. Must include a valid host.")
            }
        } catch (e: Exception) {
            throw MonetaException.ApiException(400, "Invalid base URL format: ${e.message}")
        }
    }

    // MO Info Initialization
    private suspend fun performInitialMoInfoFetch() {
        try {
            val response = membershipOrgClient!!.fetchMoInfo()
            if (response.success && response.data != null) {
                // Parse the MO info data
                val moInfo = jsonSerializer!!.decodeFromJsonElement(MoInfoResponse.serializer(), response.data)

                // Save MO info to secure storage
                secureStorage!!.saveData(moInfoCurrencyKey, moInfo.currencyCode)
                secureStorage!!.saveData(moInfoRegionKey, moInfo.region)
                secureStorage!!.saveData(initializationStateKey, "completed")

                // Mark as fully initialized
                isFullyInitialized = true
            } else {
                throw MonetaException.ApiException(
                    response.status,
                    response.message ?: "Failed to fetch MO info"
                )
            }
        } catch (e: Exception) {
            // Clean up partial initialization on failure
            baseUrl = null
            membershipOrgClient = null
            monetaCoreClient = null
            uaClient = null
            throw e
        }
    }

    // MO Info Access Methods
    fun getMoInfoCurrency(): String? {
        return secureStorage?.getData(moInfoCurrencyKey)
    }

    fun getMoInfoRegion(): String? {
        return secureStorage?.getData(moInfoRegionKey)
    }

    fun isSDKFullyInitialized(): Boolean {
        return isFullyInitialized
    }
    
    // Public API methods - iOS-style throwing methods

    // Onboarding
    suspend fun startOnboarding(): OnboardVerificationResponse {
        checkInitialized()
        return withContext(Dispatchers.IO) {
            // Generate RSA key pair
            val keyPair = generateKeyPair()

            // Store private key securely
            val privateKeyEncoded = android.util.Base64.encodeToString(
                keyPair.private.encoded,
                android.util.Base64.DEFAULT
            )
            secureStorage!!.saveData("private_key", privateKeyEncoded)

            // Create device token (in a real app, this would be a Firebase token or similar)
            val deviceToken = generateDeviceToken()
            secureStorage!!.saveData("device_token", deviceToken)

            // Create onboarding request
            val request = OnboardingRequest(
                deviceName = android.os.Build.MODEL,
                deviceToken = deviceToken,
                publicKey = android.util.Base64.encodeToString(
                    keyPair.public.encoded,
                    android.util.Base64.DEFAULT
                )
            )

            // Make API call
            val response = membershipOrgClient!!.startOnboarding(request)
            if (response.success && response.data != null) {
                response.data
            } else {
                throw MonetaException.ApiException(
                    400,
                    response.message ?: "Unknown error"
                )
            }
        }
    }

    suspend fun completeOnboarding(): OnboardUserResponse {
        checkInitialized()
        return withContext(Dispatchers.IO) {
            val response = membershipOrgClient!!.completeOnboarding()
            if (response.success && response.data != null) {
                // Store onboarding data
                secureStorage!!.saveData("onboarding_data", jsonSerializer!!.encodeToString(OnboardUserResponse.serializer(), response.data))
                response.data
            } else {
                throw MonetaException.ApiException(
                    400,
                    response.message ?: "Unknown error"
                )
            }
        }
    }

    // Transaction methods
    suspend fun getTransactions(page: Int, size: Int): PaginatedResponse<TransactionResponse> {
        checkInitialized()
        return withContext(Dispatchers.IO) {
            val response = membershipOrgClient!!.getTransactions(page, size)
            if (response.success && response.data != null) {
                response.data
            } else {
                throw MonetaException.ApiException(
                    400,
                    response.message ?: "Unknown error"
                )
            }
        }
    }

    suspend fun getTransactionLegacy(transactionId: String): TransactionResponse {
        checkInitialized()
        return withContext(Dispatchers.IO) {
            val response = membershipOrgClient!!.getTransaction(transactionId)
            if (response.success && response.data != null) {
                response.data
            } else {
                throw MonetaException.ApiException(
                    400,
                    response.message ?: "Unknown error"
                )
            }
        }
    }

    suspend fun approveSubscriptionCharge(transactionId: String, status: String): TransactionResponse {
        checkInitialized()
        return withContext(Dispatchers.IO) {
            val request = ApprovalSubscriptionChargeRequest(status = status)
            val response = membershipOrgClient!!.approveSubscriptionCharge(transactionId, request)
            if (response.success && response.data != null) {
                response.data
            } else {
                throw MonetaException.ApiException(
                    400,
                    response.message ?: "Unknown error"
                )
            }
        }
    }

    suspend fun createDispute(transactionId: String, reason: String, itemId: String? = null): Boolean {
        checkInitialized()
        return withContext(Dispatchers.IO) {
            val request = CreateDisputeRequest(
                transactionId = transactionId,
                reason = reason,
                itemId = itemId
            )
            val response = membershipOrgClient!!.createDispute(request)
            response.success
        }
    }

    suspend fun getBalance(): UserBalanceResponse {
        checkInitialized()
        return withContext(Dispatchers.IO) {
            val response = membershipOrgClient!!.fetchBalance()
            if (response.success && response.data != null) {
                response.data
            } else {
                throw MonetaException.ApiException(
                    400,
                    response.message ?: "Unknown error"
                )
            }
        }
    }

    // UA Client methods
    suspend fun getRecommendations(userId: String, userProfile: UserProfile, offset: Int, limit: Int): List<Any> {
        checkInitialized()
        return withContext(Dispatchers.IO) {
            val request = RecommendationsRequest(userId, userProfile, offset, limit)
            val response = uaClient!!.getRecommendations(request)
            if (response.success && response.data != null) {
                response.data
            } else {
                throw MonetaException.ApiException(
                    400,
                    response.message ?: "Unknown error"
                )
            }
        }
    }

    suspend fun updateUserProfile(userId: String, contentPreferences: UAUserContentPreferences): Boolean {
        checkInitialized()
        return withContext(Dispatchers.IO) {
            val request = UpdateUAUserProfileRequest(userId, contentPreferences)
            val response = uaClient!!.updateUserProfile(request)
            response.success
        }
    }

    // Publisher authentication (Legacy)
    suspend fun authPublisherLegacy(qrCodeId: String, session: String): OnboardUserResponse {
        checkInitialized()
        return withContext(Dispatchers.IO) {
            val response = membershipOrgClient!!.publisherLogin(session)
            if (response.success && response.data != null) {
                response.data
            } else {
                throw MonetaException.ApiException(
                    400,
                    response.message ?: "Unknown error"
                )
            }
        }
    }

    // ========================================
    // PRD-COMPLIANT PUBLIC API METHODS
    // ========================================

    // MembershipOrgClient Endpoints (PRD Section 4.3.1)
    suspend fun fetchMoInfo(): MonetaResult<ApiResponse<kotlinx.serialization.json.JsonElement>> {
        return MonetaResult.runCatching {
            checkInitialized()
            withContext(Dispatchers.IO) {
                membershipOrgClient!!.fetchMoInfo()
            }
        }
    }

    suspend fun verifyOnboarding(requestId: String, request: OnboardingRequest): MonetaResult<ApiResponse<OnboardVerificationResponse>> {
        return MonetaResult.runCatching {
            checkInitialized()
            withContext(Dispatchers.IO) {
                membershipOrgClient!!.verifyOnboarding(requestId, request)
            }
        }
    }

    suspend fun checkUserCodeIsVerified(requestId: String, deviceCode: String): MonetaResult<ApiResponse<OnboardUserResponse>> {
        return MonetaResult.runCatching {
            checkInitialized()
            withContext(Dispatchers.IO) {
                membershipOrgClient!!.checkUserCodeIsVerified(requestId, deviceCode)
            }
        }
    }

    suspend fun fetchTransactions(
        month: String? = null,
        pageSize: Int? = null,
        pageBefore: String? = null,
        pageAfter: String? = null
    ): MonetaResult<PaginatedResponse<TransactionResponse>> {
        return MonetaResult.runCatching {
            checkInitialized()
            withContext(Dispatchers.IO) {
                val response = membershipOrgClient!!.fetchTransactions(month, pageSize, pageBefore, pageAfter)
                if (response.success && response.data != null) {
                    response.data
                } else {
                    throw MonetaException.ApiException(
                        400,
                        response.message ?: "Unknown error"
                    )
                }
            }
        }
    }

    suspend fun getTransaction(id: String): MonetaResult<ApiResponse<TransactionResponse>> {
        return MonetaResult.runCatching {
            checkInitialized()
            withContext(Dispatchers.IO) {
                membershipOrgClient!!.getTransaction(id)
            }
        }
    }

    suspend fun getRelatedTransactions(
        id: String,
        pageSize: Int? = null,
        pageOrder: String? = null
    ): MonetaResult<PaginatedResponse<TransactionResponse>> {
        return MonetaResult.runCatching {
            checkInitialized()
            withContext(Dispatchers.IO) {
                val response = membershipOrgClient!!.getRelatedTransactions(id, pageSize, pageOrder)
                if (response.success && response.data != null) {
                    response.data
                } else {
                    throw MonetaException.ApiException(
                        400,
                        response.message ?: "Unknown error"
                    )
                }
            }
        }
    }

    suspend fun fetchUserFeeds(
        pageSize: Int? = null,
        pageAfter: String? = null
    ): MonetaResult<PaginatedResponse<UserFeedResponse>> {
        return MonetaResult.runCatching {
            checkInitialized()
            withContext(Dispatchers.IO) {
                val response = membershipOrgClient!!.fetchUserFeeds(pageSize, pageAfter)
                if (response.success && response.data != null) {
                    response.data
                } else {
                    throw MonetaException.ApiException(
                        400,
                        response.message ?: "Unknown error"
                    )
                }
            }
        }
    }

    suspend fun getFeedById(feedId: String): MonetaResult<ApiResponse<UserFeedResponse>> {
        return MonetaResult.runCatching {
            checkInitialized()
            withContext(Dispatchers.IO) {
                membershipOrgClient!!.getFeedById(feedId)
            }
        }
    }

    suspend fun markFeedsAsRead(body: Map<String, List<String>>): MonetaResult<ApiResponse<kotlinx.serialization.json.JsonElement>> {
        return MonetaResult.runCatching {
            checkInitialized()
            withContext(Dispatchers.IO) {
                membershipOrgClient!!.markFeedsAsRead(body)
            }
        }
    }

    suspend fun deleteFeeds(body: Map<String, List<String>>): MonetaResult<ApiResponse<kotlinx.serialization.json.JsonElement>> {
        return MonetaResult.runCatching {
            checkInitialized()
            withContext(Dispatchers.IO) {
                membershipOrgClient!!.deleteFeeds(body)
            }
        }
    }

    suspend fun deviceRemoval(deviceId: String): MonetaResult<ApiResponse<kotlinx.serialization.json.JsonElement>> {
        return MonetaResult.runCatching {
            checkInitialized()
            withContext(Dispatchers.IO) {
                membershipOrgClient!!.deviceRemoval(deviceId)
            }
        }
    }

    suspend fun fetchPolicies(): MonetaResult<ApiResponse<List<UserPolicyTypeResponse>>> {
        return MonetaResult.runCatching {
            checkInitialized()
            withContext(Dispatchers.IO) {
                membershipOrgClient!!.fetchPolicies()
            }
        }
    }

    suspend fun setPolicy(type: String, body: Map<String, kotlinx.serialization.json.JsonElement>): MonetaResult<ApiResponse<UserPolicyDataResponse>> {
        return MonetaResult.runCatching {
            checkInitialized()
            withContext(Dispatchers.IO) {
                membershipOrgClient!!.setPolicy(type, body)
            }
        }
    }

    suspend fun fetchIndustries(): MonetaResult<ApiResponse<List<IndustryResponse>>> {
        return MonetaResult.runCatching {
            checkInitialized()
            withContext(Dispatchers.IO) {
                membershipOrgClient!!.fetchIndustries()
            }
        }
    }

    suspend fun fetchPublishers(): MonetaResult<ApiResponse<List<IndustryResponse>>> {
        return MonetaResult.runCatching {
            checkInitialized()
            withContext(Dispatchers.IO) {
                membershipOrgClient!!.fetchPublishers()
            }
        }
    }

    suspend fun processSubscriptionCharge(id: String, request: ApprovalSubscriptionChargeRequest): MonetaResult<ApiResponse<kotlinx.serialization.json.JsonElement>> {
        return MonetaResult.runCatching {
            checkInitialized()
            withContext(Dispatchers.IO) {
                membershipOrgClient!!.processSubscriptionCharge(id, request)
            }
        }
    }

    suspend fun fetchIncrements(id: String): MonetaResult<PaginatedResponse<IncrementResponse>> {
        return MonetaResult.runCatching {
            checkInitialized()
            withContext(Dispatchers.IO) {
                val response = membershipOrgClient!!.fetchIncrements(id)
                if (response.success && response.data != null) {
                    response.data
                } else {
                    throw MonetaException.ApiException(
                        400,
                        response.message ?: "Unknown error"
                    )
                }
            }
        }
    }

    suspend fun createFetchingConsumptionActivitiesRequest(id: String): MonetaResult<ApiResponse<ConsumptionActivityStatusResponse>> {
        return MonetaResult.runCatching {
            checkInitialized()
            withContext(Dispatchers.IO) {
                membershipOrgClient!!.createFetchingConsumptionActivitiesRequest(id)
            }
        }
    }

    suspend fun checkStatusFetchingConsumptionActivitiesRequest(id: String, rid: String): MonetaResult<ApiResponse<ConsumptionActivityStatusResponse>> {
        return MonetaResult.runCatching {
            checkInitialized()
            withContext(Dispatchers.IO) {
                membershipOrgClient!!.checkStatusFetchingConsumptionActivitiesRequest(id, rid)
            }
        }
    }

    suspend fun fetchConsumptionActivities(id: String): MonetaResult<ApiResponse<List<ConsumptionActivityResponse>>> {
        return MonetaResult.runCatching {
            checkInitialized()
            withContext(Dispatchers.IO) {
                membershipOrgClient!!.fetchConsumptionActivities(id)
            }
        }
    }

    suspend fun createDisputeRequest(request: CreateDisputeRequest): MonetaResult<ApiResponse<DisputeRequestResponse>> {
        return MonetaResult.runCatching {
            checkInitialized()
            withContext(Dispatchers.IO) {
                membershipOrgClient!!.createDisputeRequest(request)
            }
        }
    }

    suspend fun fetchDisputes(transactionId: String? = null): MonetaResult<ApiResponse<List<DisputeRequestResponse>>> {
        return MonetaResult.runCatching {
            checkInitialized()
            withContext(Dispatchers.IO) {
                membershipOrgClient!!.fetchDisputes(transactionId)
            }
        }
    }

    suspend fun fetchLatestSettlementBillingCycle(): MonetaResult<ApiResponse<UserBillingCyclesResponse>> {
        return MonetaResult.runCatching {
            checkInitialized()
            withContext(Dispatchers.IO) {
                membershipOrgClient!!.fetchLatestSettlementBillingCycle()
            }
        }
    }

    suspend fun fetchSettlementBillingCycle(
        billingFrom: String? = null,
        billingTo: String? = null
    ): MonetaResult<ApiResponse<List<UserBillingCyclesResponse>>> {
        return MonetaResult.runCatching {
            checkInitialized()
            withContext(Dispatchers.IO) {
                membershipOrgClient!!.fetchSettlementBillingCycle(billingFrom, billingTo)
            }
        }
    }

    suspend fun fetchSettlementBillingCycleConsumption(code: String): MonetaResult<ApiResponse<List<ConsumptionResponse>>> {
        return MonetaResult.runCatching {
            checkInitialized()
            withContext(Dispatchers.IO) {
                membershipOrgClient!!.fetchSettlementBillingCycleConsumption(code)
            }
        }
    }

    suspend fun fetchBalance(): MonetaResult<ApiResponse<UserBalanceResponse>> {
        return MonetaResult.runCatching {
            checkInitialized()
            withContext(Dispatchers.IO) {
                membershipOrgClient!!.fetchBalance()
            }
        }
    }

    suspend fun fetchDebitHistory(): MonetaResult<ApiResponse<kotlinx.serialization.json.JsonElement>> {
        return MonetaResult.runCatching {
            checkInitialized()
            withContext(Dispatchers.IO) {
                membershipOrgClient!!.fetchDebitHistory()
            }
        }
    }

    // MonetaCoreClient Endpoints (PRD Section 4.3.2)
    suspend fun authPublisher(qrCodeId: String, request: PublisherLoginRequest): MonetaResult<PublisherLoginResponse> {
        return MonetaResult.runCatching {
            checkInitialized()
            withContext(Dispatchers.IO) {
                val response = monetaCoreClient!!.authPublisher(qrCodeId, request)
                if (response.success && response.data != null) {
                    response.data
                } else {
                    throw MonetaException.ApiException(
                        400,
                        response.message ?: "Unknown error"
                    )
                }
            }
        }
    }

    suspend fun checkPublisherLoginStatus(qrCodeId: String): MonetaResult<PublisherLoginResponse> {
        return MonetaResult.runCatching {
            checkInitialized()
            withContext(Dispatchers.IO) {
                val response = monetaCoreClient!!.checkPublisherLoginStatus(qrCodeId)
                if (response.success && response.data != null) {
                    response.data
                } else {
                    throw MonetaException.ApiException(
                        400,
                        response.message ?: "Unknown error"
                    )
                }
            }
        }
    }

    suspend fun fetchArticles(
        categories: String? = null,
        before: String? = null
    ): MonetaResult<List<ArticleResponse>> {
        return MonetaResult.runCatching {
            checkInitialized()
            withContext(Dispatchers.IO) {
                val response = monetaCoreClient!!.fetchArticles(categories, before)
                if (response.success && response.data != null) {
                    response.data
                } else {
                    throw MonetaException.ApiException(
                        400,
                        response.message ?: "Unknown error"
                    )
                }
            }
        }
    }

    suspend fun fetchArticleCategories(): MonetaResult<List<String>> {
        return MonetaResult.runCatching {
            checkInitialized()
            withContext(Dispatchers.IO) {
                val response = monetaCoreClient!!.fetchArticleCategories()
                if (response.success && response.data != null) {
                    response.data
                } else {
                    throw MonetaException.ApiException(
                        400,
                        response.message ?: "Unknown error"
                    )
                }
            }
        }
    }

    // UAClient Endpoints (PRD Section 4.3.3)
    suspend fun fetchRssContent(
        userId: String,
        limit: Int? = null,
        offset: Int? = null
    ): MonetaResult<UAResponse> {
        return MonetaResult.runCatching {
            checkInitialized()
            withContext(Dispatchers.IO) {
                val response = uaClient!!.fetchRssContent(userId, limit, offset)
                if (response.success && response.data != null) {
                    response.data
                } else {
                    throw MonetaException.ApiException(
                        400,
                        response.message ?: "Unknown error"
                    )
                }
            }
        }
    }

    suspend fun fetchRecommendations(request: RecommendationsRequest): MonetaResult<UAResponse> {
        return MonetaResult.runCatching {
            checkInitialized()
            withContext(Dispatchers.IO) {
                val response = uaClient!!.fetchRecommendations(request)
                if (response.success && response.data != null) {
                    response.data
                } else {
                    throw MonetaException.ApiException(
                        400,
                        response.message ?: "Unknown error"
                    )
                }
            }
        }
    }

    suspend fun fetchInterests(): MonetaResult<InterestsResponse> {
        return MonetaResult.runCatching {
            checkInitialized()
            withContext(Dispatchers.IO) {
                val response = uaClient!!.fetchInterests()
                if (response.success && response.data != null) {
                    response.data
                } else {
                    throw MonetaException.ApiException(
                        400,
                        response.message ?: "Unknown error"
                    )
                }
            }
        }
    }

    suspend fun fetchUserProfile(userId: String): MonetaResult<UAUserProfileResponse> {
        return MonetaResult.runCatching {
            checkInitialized()
            withContext(Dispatchers.IO) {
                val response = uaClient!!.fetchUserProfile(userId)
                if (response.success && response.data != null) {
                    response.data
                } else {
                    throw MonetaException.ApiException(
                        400,
                        response.message ?: "Unknown error"
                    )
                }
            }
        }
    }

    suspend fun updateUserProfile(request: UpdateUAUserProfileRequest): MonetaResult<UpdateUAUserProfileResponse> {
        return MonetaResult.runCatching {
            checkInitialized()
            withContext(Dispatchers.IO) {
                val response = uaClient!!.updateUserProfile(request)
                if (response.success && response.data != null) {
                    response.data
                } else {
                    throw MonetaException.ApiException(
                        400,
                        response.message ?: "Unknown error"
                    )
                }
            }
        }
    }

    // Legacy Result-based API methods for backward compatibility
    suspend fun startOnboardingResult(): MonetaResult<OnboardVerificationResponse> {
        return try {
            MonetaResult.success(startOnboarding())
        } catch (e: Exception) {
            MonetaResult.error(e)
        }
    }

    suspend fun completeOnboardingResult(): MonetaResult<OnboardUserResponse> {
        return try {
            MonetaResult.success(completeOnboarding())
        } catch (e: Exception) {
            MonetaResult.error(e)
        }
    }

    // Legacy Result-based methods for backward compatibility
    suspend fun getTransactionsResult(page: Int, size: Int): MonetaResult<PaginatedResponse<TransactionResponse>> {
        return try {
            MonetaResult.success(getTransactions(page, size))
        } catch (e: Exception) {
            MonetaResult.error(e)
        }
    }

    suspend fun getTransactionResult(transactionId: String): MonetaResult<TransactionResponse> {
        return try {
            MonetaResult.success(getTransactionLegacy(transactionId))
        } catch (e: Exception) {
            MonetaResult.error(e)
        }
    }

    suspend fun approveSubscriptionChargeResult(transactionId: String, status: String): MonetaResult<TransactionResponse> {
        return try {
            MonetaResult.success(approveSubscriptionCharge(transactionId, status))
        } catch (e: Exception) {
            MonetaResult.error(e)
        }
    }

    suspend fun createDisputeResult(transactionId: String, reason: String, itemId: String? = null): MonetaResult<Boolean> {
        return try {
            MonetaResult.success(createDispute(transactionId, reason, itemId))
        } catch (e: Exception) {
            MonetaResult.error(e)
        }
    }

    suspend fun getRecommendationsResult(userId: String, userProfile: UserProfile, offset: Int, limit: Int): MonetaResult<List<Any>> {
        return try {
            MonetaResult.success(getRecommendations(userId, userProfile, offset, limit))
        } catch (e: Exception) {
            MonetaResult.error(e)
        }
    }

    suspend fun updateUserProfileResult(userId: String, contentPreferences: UAUserContentPreferences): MonetaResult<Boolean> {
        return try {
            MonetaResult.success(updateUserProfile(userId, contentPreferences))
        } catch (e: Exception) {
            MonetaResult.error(e)
        }
    }

    suspend fun authPublisherResult(qrCodeId: String, session: String): MonetaResult<OnboardUserResponse> {
        return try {
            MonetaResult.success(authPublisherLegacy(qrCodeId, session))
        } catch (e: Exception) {
            MonetaResult.error(e)
        }
    }
    
    // Helper methods
    private fun generateKeyPair(): KeyPair {
        val keyPairGenerator = KeyPairGenerator.getInstance("RSA")
        keyPairGenerator.initialize(2048, SecureRandom())
        return keyPairGenerator.generateKeyPair()
    }
    
    private fun generateDeviceToken(): String {
        // In a real implementation, this would be a Firebase token or similar
        // For demo purposes, generate a random string
        val random = SecureRandom()
        val bytes = ByteArray(32)
        random.nextBytes(bytes)
        return android.util.Base64.encodeToString(bytes, android.util.Base64.DEFAULT)
    }
}
