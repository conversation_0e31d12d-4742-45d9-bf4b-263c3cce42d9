<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.1.2" type="incidents">

    <incident
        id="StaticFieldLeak"
        severity="warning"
        message="Do not place Android context classes in static fields (static reference to `MonetaSDK` which has field `context` pointing to `Context`); this is a memory leak">
        <location
            file="${:sdk*debug*sourceProvider*0*javaDir*2}/com/moneta/sdk/MonetaSDK.kt"
            line="25"
            column="9"
            startOffset="878"
            endLine="26"
            endColumn="33"
            endOffset="921"/>
    </incident>

</incidents>
