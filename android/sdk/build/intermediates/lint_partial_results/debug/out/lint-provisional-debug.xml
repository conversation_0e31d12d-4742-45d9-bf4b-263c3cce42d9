<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.1.2" type="conditional_incidents">

    <incident
        id="InlinedApi"
        severity="warning"
        message="">
        <fix-data minSdk="fffffffffff00000" requiresApi="ffffffffffc00000"/>
        <location
            file="${:sdk*debug*sourceProvider*0*javaDir*2}/com/moneta/sdk/internal/SecureStorage.kt"
            line="25"
            column="13"
            startOffset="761"
            endLine="25"
            endColumn="44"
            endOffset="792"/>
        <map>
            <api-levels id="minSdk"
                value="fffffffffff00000"/>
            <api-levels id="requiresApi"
                value="ffffffffffc00000"/>
            <entry
                name="message"
                string="Field requires API level 23 (current min is %1$s): `android.security.keystore.KeyProperties#KEY_ALGORITHM_AES`"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="fffffffffff00000" requiresApi="ffffffffffc00000"/>
        <location
            file="${:sdk*debug*sourceProvider*0*javaDir*2}/com/moneta/sdk/internal/SecureStorage.kt"
            line="27"
            column="55"
            startOffset="876"
            endLine="30"
            endColumn="10"
            endOffset="991"/>
        <map>
            <api-levels id="minSdk"
                value="fffffffffff00000"/>
            <api-levels id="requiresApi"
                value="ffffffffffc00000"/>
            <entry
                name="message"
                string="Call requires API level 23 (current min is %1$s): `android.security.keystore.KeyGenParameterSpec.Builder()`"/>
        </map>
    </incident>

    <incident
        id="InlinedApi"
        severity="warning"
        message="">
        <fix-data minSdk="fffffffffff00000" requiresApi="ffffffffffc00000"/>
        <location
            file="${:sdk*debug*sourceProvider*0*javaDir*2}/com/moneta/sdk/internal/SecureStorage.kt"
            line="29"
            column="13"
            startOffset="919"
            endLine="29"
            endColumn="42"
            endOffset="948"/>
        <map>
            <api-levels id="minSdk"
                value="fffffffffff00000"/>
            <api-levels id="requiresApi"
                value="ffffffffffc00000"/>
            <entry
                name="message"
                string="Field requires API level 23 (current min is %1$s): `android.security.keystore.KeyProperties#PURPOSE_ENCRYPT`"/>
        </map>
    </incident>

    <incident
        id="InlinedApi"
        severity="warning"
        message="">
        <fix-data minSdk="fffffffffff00000" requiresApi="ffffffffffc00000"/>
        <location
            file="${:sdk*debug*sourceProvider*0*javaDir*2}/com/moneta/sdk/internal/SecureStorage.kt"
            line="29"
            column="46"
            startOffset="952"
            endLine="29"
            endColumn="75"
            endOffset="981"/>
        <map>
            <api-levels id="minSdk"
                value="fffffffffff00000"/>
            <api-levels id="requiresApi"
                value="ffffffffffc00000"/>
            <entry
                name="message"
                string="Field requires API level 23 (current min is %1$s): `android.security.keystore.KeyProperties#PURPOSE_DECRYPT`"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="fffffffffff00000" requiresApi="ffffffffffc00000"/>
        <location
            file="${:sdk*debug*sourceProvider*0*javaDir*2}/com/moneta/sdk/internal/SecureStorage.kt"
            line="31"
            column="14"
            startOffset="1005"
            endLine="31"
            endColumn="27"
            endOffset="1018"/>
        <map>
            <api-levels id="minSdk"
                value="fffffffffff00000"/>
            <api-levels id="requiresApi"
                value="ffffffffffc00000"/>
            <entry
                name="message"
                string="Call requires API level 23 (current min is %1$s): `android.security.keystore.KeyGenParameterSpec.Builder#setBlockModes`"/>
        </map>
    </incident>

    <incident
        id="InlinedApi"
        severity="warning"
        message="">
        <fix-data minSdk="fffffffffff00000" requiresApi="ffffffffffc00000"/>
        <location
            file="${:sdk*debug*sourceProvider*0*javaDir*2}/com/moneta/sdk/internal/SecureStorage.kt"
            line="31"
            column="28"
            startOffset="1019"
            endLine="31"
            endColumn="56"
            endOffset="1047"/>
        <map>
            <api-levels id="minSdk"
                value="fffffffffff00000"/>
            <api-levels id="requiresApi"
                value="ffffffffffc00000"/>
            <entry
                name="message"
                string="Field requires API level 23 (current min is %1$s): `android.security.keystore.KeyProperties#BLOCK_MODE_GCM`"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="fffffffffff00000" requiresApi="ffffffffffc00000"/>
        <location
            file="${:sdk*debug*sourceProvider*0*javaDir*2}/com/moneta/sdk/internal/SecureStorage.kt"
            line="32"
            column="14"
            startOffset="1062"
            endLine="32"
            endColumn="35"
            endOffset="1083"/>
        <map>
            <api-levels id="minSdk"
                value="fffffffffff00000"/>
            <api-levels id="requiresApi"
                value="ffffffffffc00000"/>
            <entry
                name="message"
                string="Call requires API level 23 (current min is %1$s): `android.security.keystore.KeyGenParameterSpec.Builder#setEncryptionPaddings`"/>
        </map>
    </incident>

    <incident
        id="InlinedApi"
        severity="warning"
        message="">
        <fix-data minSdk="fffffffffff00000" requiresApi="ffffffffffc00000"/>
        <location
            file="${:sdk*debug*sourceProvider*0*javaDir*2}/com/moneta/sdk/internal/SecureStorage.kt"
            line="32"
            column="36"
            startOffset="1084"
            endLine="32"
            endColumn="73"
            endOffset="1121"/>
        <map>
            <api-levels id="minSdk"
                value="fffffffffff00000"/>
            <api-levels id="requiresApi"
                value="ffffffffffc00000"/>
            <entry
                name="message"
                string="Field requires API level 23 (current min is %1$s): `android.security.keystore.KeyProperties#ENCRYPTION_PADDING_NONE`"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="fffffffffff00000" requiresApi="ffffffffffc00000"/>
        <location
            file="${:sdk*debug*sourceProvider*0*javaDir*2}/com/moneta/sdk/internal/SecureStorage.kt"
            line="33"
            column="14"
            startOffset="1136"
            endLine="33"
            endColumn="45"
            endOffset="1167"/>
        <map>
            <api-levels id="minSdk"
                value="fffffffffff00000"/>
            <api-levels id="requiresApi"
                value="ffffffffffc00000"/>
            <entry
                name="message"
                string="Call requires API level 23 (current min is %1$s): `android.security.keystore.KeyGenParameterSpec.Builder#setRandomizedEncryptionRequired`"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="fffffffffff00000" requiresApi="ffffffffffc00000"/>
        <location
            file="${:sdk*debug*sourceProvider*0*javaDir*2}/com/moneta/sdk/internal/SecureStorage.kt"
            line="34"
            column="14"
            startOffset="1187"
            endLine="34"
            endColumn="19"
            endOffset="1192"/>
        <map>
            <api-levels id="minSdk"
                value="fffffffffff00000"/>
            <api-levels id="requiresApi"
                value="ffffffffffc00000"/>
            <entry
                name="message"
                string="Call requires API level 23 (current min is %1$s): `android.security.keystore.KeyGenParameterSpec.Builder#build`"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="fffffffffff00000" requiresApi="ffffffffffc00000"/>
        <location
            file="${:sdk*debug*sourceProvider*0*javaDir*2}/com/moneta/sdk/internal/SecureStorage.kt"
            line="36"
            column="27"
            startOffset="1230"
            endLine="36"
            endColumn="46"
            endOffset="1249"/>
        <map>
            <api-levels id="minSdk"
                value="fffffffffff00000"/>
            <api-levels id="requiresApi"
                value="ffffffffffc00000"/>
            <entry
                name="message"
                string="Cast from `KeyGenParameterSpec` to `AlgorithmParameterSpec` requires API level 23 (current min is %1$s)"/>
        </map>
    </incident>

</incidents>
