<lint-module
    format="1"
    dir="/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android/sdk"
    name=":sdk"
    type="LIBRARY"
    maven="MonetaSDK:sdk:"
    agpVersion="8.1.2"
    buildFolder="build"
    bootClassPath="/Users/<USER>/Library/Android/sdk/platforms/android-34/android.jar:/Users/<USER>/Library/Android/sdk/build-tools/33.0.1/core-lambda-stubs.jar"
    javaSourceLevel="1.8"
    compileTarget="android-34"
    neverShrinking="true">
  <lintOptions
      abortOnError="true"
      absolutePaths="true"
      checkReleaseBuilds="true"
      explainIssues="true"/>
  <variant name="debug"/>
</lint-module>
