<dependencies>
  <compile
      roots="/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android@@:sdk::debug,androidx.core:core-ktx:1.12.0@aar,com.squareup.okhttp3:okhttp:4.12.0@jar,org.jetbrains.kotlinx:kotlinx-coroutines-test-jvm:1.7.3@jar,org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar,org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar,com.squareup.okio:okio-jvm:3.6.0@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.10@jar,org.jetbrains.kotlinx:kotlinx-serialization-core-jvm:1.6.0@jar,org.jetbrains.kotlinx:kotlinx-serialization-json-jvm:1.6.0@jar,androidx.core:core:1.12.0@aar,androidx.lifecycle:lifecycle-runtime:2.3.1@aar,androidx.versionedparcelable:versionedparcelable:1.1.1@aar,androidx.lifecycle:lifecycle-common:2.3.1@jar,androidx.arch.core:core-common:2.1.0@jar,androidx.collection:collection:1.0.0@jar,androidx.annotation:annotation-jvm:1.6.0@jar,androidx.annotation:annotation-experimental:1.3.0@aar,org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.10@jar,org.jetbrains.kotlin:kotlin-stdlib:1.9.20@jar,junit:junit:4.13.2@jar,org.hamcrest:hamcrest-core:1.3@jar,org.mockito.kotlin:mockito-kotlin:5.2.1@jar,org.mockito:mockito-core:5.7.0@jar,net.bytebuddy:byte-buddy:1.14.9@jar,net.bytebuddy:byte-buddy-agent:1.14.9@jar,org.jetbrains:annotations:23.0.0@jar">
    <dependency
        name="/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android@@:sdk::debug"
        simpleName="MonetaSDK:sdk"/>
    <dependency
        name="androidx.core:core-ktx:1.12.0@aar"
        simpleName="androidx.core:core-ktx"/>
    <dependency
        name="com.squareup.okhttp3:okhttp:4.12.0@jar"
        simpleName="com.squareup.okhttp3:okhttp"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-test-jvm:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-test-jvm"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-android"/>
    <dependency
        name="com.squareup.okio:okio-jvm:3.6.0@jar"
        simpleName="com.squareup.okio:okio-jvm"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.10@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk8"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-serialization-core-jvm:1.6.0@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-serialization-core-jvm"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-serialization-json-jvm:1.6.0@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-serialization-json-jvm"/>
    <dependency
        name="androidx.core:core:1.12.0@aar"
        simpleName="androidx.core:core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime:2.3.1@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime"/>
    <dependency
        name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
        simpleName="androidx.versionedparcelable:versionedparcelable"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common:2.3.1@jar"
        simpleName="androidx.lifecycle:lifecycle-common"/>
    <dependency
        name="androidx.arch.core:core-common:2.1.0@jar"
        simpleName="androidx.arch.core:core-common"/>
    <dependency
        name="androidx.collection:collection:1.0.0@jar"
        simpleName="androidx.collection:collection"/>
    <dependency
        name="androidx.annotation:annotation-jvm:1.6.0@jar"
        simpleName="androidx.annotation:annotation-jvm"/>
    <dependency
        name="androidx.annotation:annotation-experimental:1.3.0@aar"
        simpleName="androidx.annotation:annotation-experimental"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.10@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk7"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib:1.9.20@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib"/>
    <dependency
        name="junit:junit:4.13.2@jar"
        simpleName="junit:junit"/>
    <dependency
        name="org.hamcrest:hamcrest-core:1.3@jar"
        simpleName="org.hamcrest:hamcrest-core"/>
    <dependency
        name="org.mockito.kotlin:mockito-kotlin:5.2.1@jar"
        simpleName="org.mockito.kotlin:mockito-kotlin"/>
    <dependency
        name="org.mockito:mockito-core:5.7.0@jar"
        simpleName="org.mockito:mockito-core"/>
    <dependency
        name="net.bytebuddy:byte-buddy:1.14.9@jar"
        simpleName="net.bytebuddy:byte-buddy"/>
    <dependency
        name="net.bytebuddy:byte-buddy-agent:1.14.9@jar"
        simpleName="net.bytebuddy:byte-buddy-agent"/>
    <dependency
        name="org.jetbrains:annotations:23.0.0@jar"
        simpleName="org.jetbrains:annotations"/>
  </compile>
  <package
      roots="/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android@@:sdk::debug,junit:junit:4.13.2@jar,org.mockito.kotlin:mockito-kotlin:5.2.1@jar,org.mockito:mockito-core:5.7.0@jar,org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar,org.jetbrains.kotlinx:kotlinx-coroutines-test-jvm:1.7.3@jar,org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar,androidx.core:core:1.12.0@aar,androidx.core:core-ktx:1.12.0@aar,org.jetbrains.kotlinx:kotlinx-serialization-core-jvm:1.6.0@jar,org.jetbrains.kotlinx:kotlinx-serialization-json-jvm:1.6.0@jar,androidx.annotation:annotation-experimental:1.3.0@aar,com.squareup.okhttp3:okhttp:4.12.0@jar,com.squareup.okio:okio-jvm:3.6.0@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.10@jar,androidx.versionedparcelable:versionedparcelable:1.1.1@aar,androidx.collection:collection:1.0.0@jar,androidx.concurrent:concurrent-futures:1.0.0@jar,androidx.interpolator:interpolator:1.0.0@aar,androidx.lifecycle:lifecycle-runtime:2.3.1@aar,androidx.arch.core:core-runtime:2.1.0@aar,androidx.lifecycle:lifecycle-common:2.3.1@jar,androidx.arch.core:core-common:2.1.0@jar,androidx.annotation:annotation-jvm:1.6.0@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.10@jar,org.jetbrains.kotlin:kotlin-stdlib:1.9.20@jar,org.hamcrest:hamcrest-core:1.3@jar,net.bytebuddy:byte-buddy:1.14.9@jar,net.bytebuddy:byte-buddy-agent:1.14.9@jar,org.objenesis:objenesis:3.3@jar,org.jetbrains:annotations:23.0.0@jar,com.google.guava:listenablefuture:1.0@jar">
    <dependency
        name="/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android@@:sdk::debug"
        simpleName="MonetaSDK:sdk"/>
    <dependency
        name="junit:junit:4.13.2@jar"
        simpleName="junit:junit"/>
    <dependency
        name="org.mockito.kotlin:mockito-kotlin:5.2.1@jar"
        simpleName="org.mockito.kotlin:mockito-kotlin"/>
    <dependency
        name="org.mockito:mockito-core:5.7.0@jar"
        simpleName="org.mockito:mockito-core"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-android"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-test-jvm:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-test-jvm"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm"/>
    <dependency
        name="androidx.core:core:1.12.0@aar"
        simpleName="androidx.core:core"/>
    <dependency
        name="androidx.core:core-ktx:1.12.0@aar"
        simpleName="androidx.core:core-ktx"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-serialization-core-jvm:1.6.0@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-serialization-core-jvm"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-serialization-json-jvm:1.6.0@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-serialization-json-jvm"/>
    <dependency
        name="androidx.annotation:annotation-experimental:1.3.0@aar"
        simpleName="androidx.annotation:annotation-experimental"/>
    <dependency
        name="com.squareup.okhttp3:okhttp:4.12.0@jar"
        simpleName="com.squareup.okhttp3:okhttp"/>
    <dependency
        name="com.squareup.okio:okio-jvm:3.6.0@jar"
        simpleName="com.squareup.okio:okio-jvm"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.10@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk8"/>
    <dependency
        name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
        simpleName="androidx.versionedparcelable:versionedparcelable"/>
    <dependency
        name="androidx.collection:collection:1.0.0@jar"
        simpleName="androidx.collection:collection"/>
    <dependency
        name="androidx.concurrent:concurrent-futures:1.0.0@jar"
        simpleName="androidx.concurrent:concurrent-futures"/>
    <dependency
        name="androidx.interpolator:interpolator:1.0.0@aar"
        simpleName="androidx.interpolator:interpolator"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime:2.3.1@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime"/>
    <dependency
        name="androidx.arch.core:core-runtime:2.1.0@aar"
        simpleName="androidx.arch.core:core-runtime"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common:2.3.1@jar"
        simpleName="androidx.lifecycle:lifecycle-common"/>
    <dependency
        name="androidx.arch.core:core-common:2.1.0@jar"
        simpleName="androidx.arch.core:core-common"/>
    <dependency
        name="androidx.annotation:annotation-jvm:1.6.0@jar"
        simpleName="androidx.annotation:annotation-jvm"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.10@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk7"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib:1.9.20@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib"/>
    <dependency
        name="org.hamcrest:hamcrest-core:1.3@jar"
        simpleName="org.hamcrest:hamcrest-core"/>
    <dependency
        name="net.bytebuddy:byte-buddy:1.14.9@jar"
        simpleName="net.bytebuddy:byte-buddy"/>
    <dependency
        name="net.bytebuddy:byte-buddy-agent:1.14.9@jar"
        simpleName="net.bytebuddy:byte-buddy-agent"/>
    <dependency
        name="org.objenesis:objenesis:3.3@jar"
        simpleName="org.objenesis:objenesis"/>
    <dependency
        name="org.jetbrains:annotations:23.0.0@jar"
        simpleName="org.jetbrains:annotations"/>
    <dependency
        name="com.google.guava:listenablefuture:1.0@jar"
        simpleName="com.google.guava:listenablefuture"/>
  </package>
</dependencies>
