<dependencies>
  <compile
      roots="/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android@@:sdk::debug,androidx.test.ext:junit:1.1.5@aar,androidx.test.espresso:espresso-core:3.5.1@aar,androidx.core:core-ktx:1.12.0@aar,androidx.test:core:1.5.0@aar,com.squareup.okhttp3:okhttp:4.12.0@jar,org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar,org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar,com.squareup.okio:okio-jvm:3.6.0@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.10@jar,org.jetbrains.kotlinx:kotlinx-serialization-core-jvm:1.6.0@jar,org.jetbrains.kotlinx:kotlinx-serialization-json-jvm:1.6.0@jar,androidx.test:runner:1.5.2@aar,androidx.test.services:storage:1.4.2@aar,androidx.test:monitor:1.6.1@aar,androidx.test:annotation:1.0.1@aar,androidx.core:core:1.12.0@aar,androidx.lifecycle:lifecycle-runtime:2.3.1@aar,androidx.lifecycle:lifecycle-common:2.3.1@jar,androidx.concurrent:concurrent-futures:1.0.0@jar,androidx.versionedparcelable:versionedparcelable:1.1.1@aar,androidx.arch.core:core-common:2.1.0@jar,androidx.collection:collection:1.0.0@jar,androidx.annotation:annotation-jvm:1.6.0@jar,androidx.annotation:annotation-experimental:1.3.0@aar,org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.10@jar,org.jetbrains.kotlin:kotlin-stdlib:1.9.20@jar,junit:junit:4.13.2@jar,org.hamcrest:hamcrest-integration:1.3@jar,org.hamcrest:hamcrest-library:1.3@jar,org.hamcrest:hamcrest-core:1.3@jar,org.jetbrains:annotations:23.0.0@jar,androidx.tracing:tracing:1.0.0@aar,com.google.code.findbugs:jsr305:2.0.2@jar,com.google.guava:listenablefuture:1.0@jar,androidx.test.espresso:espresso-idling-resource:3.5.1@aar,com.squareup:javawriter:2.1.1@jar,javax.inject:javax.inject:1@jar">
    <dependency
        name="/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android@@:sdk::debug"
        simpleName="MonetaSDK:sdk"/>
    <dependency
        name="androidx.test.ext:junit:1.1.5@aar"
        simpleName="androidx.test.ext:junit"/>
    <dependency
        name="androidx.test.espresso:espresso-core:3.5.1@aar"
        simpleName="androidx.test.espresso:espresso-core"/>
    <dependency
        name="androidx.core:core-ktx:1.12.0@aar"
        simpleName="androidx.core:core-ktx"/>
    <dependency
        name="androidx.test:core:1.5.0@aar"
        simpleName="androidx.test:core"/>
    <dependency
        name="com.squareup.okhttp3:okhttp:4.12.0@jar"
        simpleName="com.squareup.okhttp3:okhttp"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-android"/>
    <dependency
        name="com.squareup.okio:okio-jvm:3.6.0@jar"
        simpleName="com.squareup.okio:okio-jvm"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.10@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk8"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-serialization-core-jvm:1.6.0@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-serialization-core-jvm"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-serialization-json-jvm:1.6.0@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-serialization-json-jvm"/>
    <dependency
        name="androidx.test:runner:1.5.2@aar"
        simpleName="androidx.test:runner"/>
    <dependency
        name="androidx.test.services:storage:1.4.2@aar"
        simpleName="androidx.test.services:storage"/>
    <dependency
        name="androidx.test:monitor:1.6.1@aar"
        simpleName="androidx.test:monitor"/>
    <dependency
        name="androidx.test:annotation:1.0.1@aar"
        simpleName="androidx.test:annotation"/>
    <dependency
        name="androidx.core:core:1.12.0@aar"
        simpleName="androidx.core:core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime:2.3.1@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common:2.3.1@jar"
        simpleName="androidx.lifecycle:lifecycle-common"/>
    <dependency
        name="androidx.concurrent:concurrent-futures:1.0.0@jar"
        simpleName="androidx.concurrent:concurrent-futures"/>
    <dependency
        name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
        simpleName="androidx.versionedparcelable:versionedparcelable"/>
    <dependency
        name="androidx.arch.core:core-common:2.1.0@jar"
        simpleName="androidx.arch.core:core-common"/>
    <dependency
        name="androidx.collection:collection:1.0.0@jar"
        simpleName="androidx.collection:collection"/>
    <dependency
        name="androidx.annotation:annotation-jvm:1.6.0@jar"
        simpleName="androidx.annotation:annotation-jvm"/>
    <dependency
        name="androidx.annotation:annotation-experimental:1.3.0@aar"
        simpleName="androidx.annotation:annotation-experimental"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.10@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk7"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib:1.9.20@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib"/>
    <dependency
        name="junit:junit:4.13.2@jar"
        simpleName="junit:junit"/>
    <dependency
        name="org.hamcrest:hamcrest-integration:1.3@jar"
        simpleName="org.hamcrest:hamcrest-integration"/>
    <dependency
        name="org.hamcrest:hamcrest-library:1.3@jar"
        simpleName="org.hamcrest:hamcrest-library"/>
    <dependency
        name="org.hamcrest:hamcrest-core:1.3@jar"
        simpleName="org.hamcrest:hamcrest-core"/>
    <dependency
        name="org.jetbrains:annotations:23.0.0@jar"
        simpleName="org.jetbrains:annotations"/>
    <dependency
        name="androidx.tracing:tracing:1.0.0@aar"
        simpleName="androidx.tracing:tracing"/>
    <dependency
        name="com.google.code.findbugs:jsr305:2.0.2@jar"
        simpleName="com.google.code.findbugs:jsr305"/>
    <dependency
        name="com.google.guava:listenablefuture:1.0@jar"
        simpleName="com.google.guava:listenablefuture"/>
    <dependency
        name="androidx.test.espresso:espresso-idling-resource:3.5.1@aar"
        simpleName="androidx.test.espresso:espresso-idling-resource"/>
    <dependency
        name="com.squareup:javawriter:2.1.1@jar"
        simpleName="com.squareup:javawriter"/>
    <dependency
        name="javax.inject:javax.inject:1@jar"
        simpleName="javax.inject:javax.inject"/>
  </compile>
  <package
      roots="/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android@@:sdk::debug,androidx.test.ext:junit:1.1.5@aar,androidx.test.espresso:espresso-core:3.5.1@aar,androidx.test:core:1.5.0@aar,androidx.core:core:1.12.0@aar,androidx.core:core-ktx:1.12.0@aar,androidx.test:runner:1.5.2@aar,androidx.test.services:storage:1.4.2@aar,androidx.test:monitor:1.6.1@aar,androidx.test:annotation:1.0.1@aar,androidx.tracing:tracing:1.0.0@aar,androidx.lifecycle:lifecycle-runtime:2.3.1@aar,androidx.lifecycle:lifecycle-common:2.3.1@jar,androidx.concurrent:concurrent-futures:1.0.0@jar,androidx.versionedparcelable:versionedparcelable:1.1.1@aar,androidx.collection:collection:1.0.0@jar,androidx.interpolator:interpolator:1.0.0@aar,androidx.arch.core:core-runtime:2.1.0@aar,androidx.arch.core:core-common:2.1.0@jar,androidx.annotation:annotation-jvm:1.6.0@jar,androidx.annotation:annotation-experimental:1.3.0@aar,com.squareup.okhttp3:okhttp:4.12.0@jar,org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar,org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar,com.squareup.okio:okio-jvm:3.6.0@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.10@jar,org.jetbrains.kotlinx:kotlinx-serialization-core-jvm:1.6.0@jar,org.jetbrains.kotlinx:kotlinx-serialization-json-jvm:1.6.0@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.10@jar,org.jetbrains.kotlin:kotlin-stdlib:1.9.20@jar,org.jetbrains:annotations:23.0.0@jar,junit:junit:4.13.2@jar,androidx.test.espresso:espresso-idling-resource:3.5.1@aar,com.squareup:javawriter:2.1.1@jar,javax.inject:javax.inject:1@jar,org.hamcrest:hamcrest-integration:1.3@jar,org.hamcrest:hamcrest-library:1.3@jar,com.google.code.findbugs:jsr305:2.0.2@jar,org.hamcrest:hamcrest-core:1.3@jar,com.google.guava:listenablefuture:1.0@jar">
    <dependency
        name="/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android@@:sdk::debug"
        simpleName="MonetaSDK:sdk"/>
    <dependency
        name="androidx.test.ext:junit:1.1.5@aar"
        simpleName="androidx.test.ext:junit"/>
    <dependency
        name="androidx.test.espresso:espresso-core:3.5.1@aar"
        simpleName="androidx.test.espresso:espresso-core"/>
    <dependency
        name="androidx.test:core:1.5.0@aar"
        simpleName="androidx.test:core"/>
    <dependency
        name="androidx.core:core:1.12.0@aar"
        simpleName="androidx.core:core"/>
    <dependency
        name="androidx.core:core-ktx:1.12.0@aar"
        simpleName="androidx.core:core-ktx"/>
    <dependency
        name="androidx.test:runner:1.5.2@aar"
        simpleName="androidx.test:runner"/>
    <dependency
        name="androidx.test.services:storage:1.4.2@aar"
        simpleName="androidx.test.services:storage"/>
    <dependency
        name="androidx.test:monitor:1.6.1@aar"
        simpleName="androidx.test:monitor"/>
    <dependency
        name="androidx.test:annotation:1.0.1@aar"
        simpleName="androidx.test:annotation"/>
    <dependency
        name="androidx.tracing:tracing:1.0.0@aar"
        simpleName="androidx.tracing:tracing"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime:2.3.1@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common:2.3.1@jar"
        simpleName="androidx.lifecycle:lifecycle-common"/>
    <dependency
        name="androidx.concurrent:concurrent-futures:1.0.0@jar"
        simpleName="androidx.concurrent:concurrent-futures"/>
    <dependency
        name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
        simpleName="androidx.versionedparcelable:versionedparcelable"/>
    <dependency
        name="androidx.collection:collection:1.0.0@jar"
        simpleName="androidx.collection:collection"/>
    <dependency
        name="androidx.interpolator:interpolator:1.0.0@aar"
        simpleName="androidx.interpolator:interpolator"/>
    <dependency
        name="androidx.arch.core:core-runtime:2.1.0@aar"
        simpleName="androidx.arch.core:core-runtime"/>
    <dependency
        name="androidx.arch.core:core-common:2.1.0@jar"
        simpleName="androidx.arch.core:core-common"/>
    <dependency
        name="androidx.annotation:annotation-jvm:1.6.0@jar"
        simpleName="androidx.annotation:annotation-jvm"/>
    <dependency
        name="androidx.annotation:annotation-experimental:1.3.0@aar"
        simpleName="androidx.annotation:annotation-experimental"/>
    <dependency
        name="com.squareup.okhttp3:okhttp:4.12.0@jar"
        simpleName="com.squareup.okhttp3:okhttp"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-android"/>
    <dependency
        name="com.squareup.okio:okio-jvm:3.6.0@jar"
        simpleName="com.squareup.okio:okio-jvm"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.10@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk8"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-serialization-core-jvm:1.6.0@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-serialization-core-jvm"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-serialization-json-jvm:1.6.0@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-serialization-json-jvm"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.10@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk7"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib:1.9.20@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib"/>
    <dependency
        name="org.jetbrains:annotations:23.0.0@jar"
        simpleName="org.jetbrains:annotations"/>
    <dependency
        name="junit:junit:4.13.2@jar"
        simpleName="junit:junit"/>
    <dependency
        name="androidx.test.espresso:espresso-idling-resource:3.5.1@aar"
        simpleName="androidx.test.espresso:espresso-idling-resource"/>
    <dependency
        name="com.squareup:javawriter:2.1.1@jar"
        simpleName="com.squareup:javawriter"/>
    <dependency
        name="javax.inject:javax.inject:1@jar"
        simpleName="javax.inject:javax.inject"/>
    <dependency
        name="org.hamcrest:hamcrest-integration:1.3@jar"
        simpleName="org.hamcrest:hamcrest-integration"/>
    <dependency
        name="org.hamcrest:hamcrest-library:1.3@jar"
        simpleName="org.hamcrest:hamcrest-library"/>
    <dependency
        name="com.google.code.findbugs:jsr305:2.0.2@jar"
        simpleName="com.google.code.findbugs:jsr305"/>
    <dependency
        name="org.hamcrest:hamcrest-core:1.3@jar"
        simpleName="org.hamcrest:hamcrest-core"/>
    <dependency
        name="com.google.guava:listenablefuture:1.0@jar"
        simpleName="com.google.guava:listenablefuture"/>
  </package>
</dependencies>
