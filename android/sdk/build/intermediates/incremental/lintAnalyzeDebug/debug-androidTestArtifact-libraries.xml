<libraries>
  <library
      name="androidx.core:core-ktx:1.12.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.12/transforms/7ebdbedc48c0fe6597efbcb4ca42db67/transformed/jetified-core-ktx-1.12.0/jars/classes.jar"
      resolved="androidx.core:core-ktx:1.12.0"
      folder="/Users/<USER>/.gradle/caches/8.12/transforms/7ebdbedc48c0fe6597efbcb4ca42db67/transformed/jetified-core-ktx-1.12.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.squareup.okhttp3:okhttp:4.12.0@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.squareup.okhttp3/okhttp/4.12.0/2f4525d4a200e97e1b87449c2cd9bd2e25b7e8cd/okhttp-4.12.0.jar"
      resolved="com.squareup.okhttp3:okhttp:4.12.0"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.kotlinx/kotlinx-coroutines-core-jvm/1.7.3/2b09627576f0989a436a00a4a54b55fa5026fb86/kotlinx-coroutines-core-jvm-1.7.3.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.kotlinx/kotlinx-coroutines-android/1.7.3/38d9cad3a0b03a10453b56577984bdeb48edeed5/kotlinx-coroutines-android-1.7.3.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3"/>
  <library
      name="com.squareup.okio:okio-jvm:3.6.0@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.squareup.okio/okio-jvm/3.6.0/5600569133b7bdefe1daf9ec7f4abeb6d13e1786/okio-jvm-3.6.0.jar"
      resolved="com.squareup.okio:okio-jvm:3.6.0"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.10@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.kotlin/kotlin-stdlib-jdk8/1.9.10/c7510d64a83411a649c76f2778304ddf71d7437b/kotlin-stdlib-jdk8-1.9.10.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.10"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-serialization-core-jvm:1.6.0@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.kotlinx/kotlinx-serialization-core-jvm/1.6.0/94b35f721f1029b3b8d46fb277b4e53fb1a0a510/kotlinx-serialization-core-jvm-1.6.0.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-serialization-core-jvm:1.6.0"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-serialization-json-jvm:1.6.0@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.kotlinx/kotlinx-serialization-json-jvm/1.6.0/59761a941bd8691b3ef2447f5b9376868a6b2d7e/kotlinx-serialization-json-jvm-1.6.0.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-serialization-json-jvm:1.6.0"/>
  <library
      name="androidx.core:core:1.12.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.12/transforms/b52eefb7eefa5c6ad84a8949ac37e1b4/transformed/core-1.12.0/jars/classes.jar"
      resolved="androidx.core:core:1.12.0"
      folder="/Users/<USER>/.gradle/caches/8.12/transforms/b52eefb7eefa5c6ad84a8949ac37e1b4/transformed/core-1.12.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime:2.3.1@aar"
      jars="/Users/<USER>/.gradle/caches/8.12/transforms/6722149b4868a04b353480b85bad5218/transformed/lifecycle-runtime-2.3.1/jars/classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime:2.3.1"
      folder="/Users/<USER>/.gradle/caches/8.12/transforms/6722149b4868a04b353480b85bad5218/transformed/lifecycle-runtime-2.3.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
      jars="/Users/<USER>/.gradle/caches/8.12/transforms/d78b48e300ce6c9a51bb25e989698fc0/transformed/versionedparcelable-1.1.1/jars/classes.jar"
      resolved="androidx.versionedparcelable:versionedparcelable:1.1.1"
      folder="/Users/<USER>/.gradle/caches/8.12/transforms/d78b48e300ce6c9a51bb25e989698fc0/transformed/versionedparcelable-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-common:2.3.1@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/androidx.lifecycle/lifecycle-common/2.3.1/fc466261d52f4433863642fb40d12441ae274a98/lifecycle-common-2.3.1.jar"
      resolved="androidx.lifecycle:lifecycle-common:2.3.1"/>
  <library
      name="androidx.arch.core:core-common:2.1.0@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/androidx.arch.core/core-common/2.1.0/b3152fc64428c9354344bd89848ecddc09b6f07e/core-common-2.1.0.jar"
      resolved="androidx.arch.core:core-common:2.1.0"/>
  <library
      name="androidx.collection:collection:1.0.0@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/androidx.collection/collection/1.0.0/42858b26cafdaa69b6149f45dfc2894007bc2c7a/collection-1.0.0.jar"
      resolved="androidx.collection:collection:1.0.0"/>
  <library
      name="androidx.annotation:annotation-jvm:1.6.0@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/androidx.annotation/annotation-jvm/1.6.0/a7257339a052df0f91433cf9651231bbb802b502/annotation-jvm-1.6.0.jar"
      resolved="androidx.annotation:annotation-jvm:1.6.0"/>
  <library
      name="androidx.annotation:annotation-experimental:1.3.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.12/transforms/59e52510e7060bed3731d040990aa23c/transformed/jetified-annotation-experimental-1.3.0/jars/classes.jar"
      resolved="androidx.annotation:annotation-experimental:1.3.0"
      folder="/Users/<USER>/.gradle/caches/8.12/transforms/59e52510e7060bed3731d040990aa23c/transformed/jetified-annotation-experimental-1.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.10@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.kotlin/kotlin-stdlib-jdk7/1.9.10/bc5bfc2690338defd5195b05c57562f2194eeb10/kotlin-stdlib-jdk7-1.9.10.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.10"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib:1.9.20@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.kotlin/kotlin-stdlib/1.9.20/e58b4816ac517e9cc5df1db051120c63d4cde669/kotlin-stdlib-1.9.20.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib:1.9.20"/>
  <library
      name="org.jetbrains:annotations:23.0.0@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains/annotations/23.0.0/8cc20c07506ec18e0834947b84a864bfc094484e/annotations-23.0.0.jar"
      resolved="org.jetbrains:annotations:23.0.0"/>
  <library
      name="androidx.concurrent:concurrent-futures:1.0.0@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/androidx.concurrent/concurrent-futures/1.0.0/c1e77e3ee6f4643b77496a1ddf7a2eef1aefdaa1/concurrent-futures-1.0.0.jar"
      resolved="androidx.concurrent:concurrent-futures:1.0.0"/>
  <library
      name="androidx.interpolator:interpolator:1.0.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.12/transforms/dba0856e066e5109e479c688e8875839/transformed/interpolator-1.0.0/jars/classes.jar"
      resolved="androidx.interpolator:interpolator:1.0.0"
      folder="/Users/<USER>/.gradle/caches/8.12/transforms/dba0856e066e5109e479c688e8875839/transformed/interpolator-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.arch.core:core-runtime:2.1.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.12/transforms/0a666fbb772e09d664c7f85540c9b402/transformed/core-runtime-2.1.0/jars/classes.jar"
      resolved="androidx.arch.core:core-runtime:2.1.0"
      folder="/Users/<USER>/.gradle/caches/8.12/transforms/0a666fbb772e09d664c7f85540c9b402/transformed/core-runtime-2.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.guava:listenablefuture:1.0@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.google.guava/listenablefuture/1.0/c949a840a6acbc5268d088e47b04177bf90b3cad/listenablefuture-1.0.jar"
      resolved="com.google.guava:listenablefuture:1.0"/>
  <library
      name="/Users/<USER>/Documents/augment-projects/moneta-mobile-sdks/android@@:sdk::debug"
      jars="/Users/<USER>/.gradle/caches/8.12/transforms/2647bd49a68d009276f407cdd62dc888/transformed/out/jars/classes.jar:/Users/<USER>/.gradle/caches/8.12/transforms/2647bd49a68d009276f407cdd62dc888/transformed/out/jars/libs/R.jar"
      resolved="MonetaSDK:sdk:unspecified"
      folder="/Users/<USER>/.gradle/caches/8.12/transforms/2647bd49a68d009276f407cdd62dc888/transformed/out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="junit:junit:4.13.2@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/junit/junit/4.13.2/8ac9e16d933b6fb43bc7f576336b8f4d7eb5ba12/junit-4.13.2.jar"
      resolved="junit:junit:4.13.2"/>
  <library
      name="org.hamcrest:hamcrest-core:1.3@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.hamcrest/hamcrest-core/1.3/42a25dc3219429f0e5d060061f71acb49bf010a0/hamcrest-core-1.3.jar"
      resolved="org.hamcrest:hamcrest-core:1.3"/>
  <library
      name="androidx.test.ext:junit:1.1.5@aar"
      jars="/Users/<USER>/.gradle/caches/8.12/transforms/181fec87cca7bbc4093b6f00d2beb380/transformed/jetified-junit-1.1.5/jars/classes.jar"
      resolved="androidx.test.ext:junit:1.1.5"
      folder="/Users/<USER>/.gradle/caches/8.12/transforms/181fec87cca7bbc4093b6f00d2beb380/transformed/jetified-junit-1.1.5"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.test.espresso:espresso-core:3.5.1@aar"
      jars="/Users/<USER>/.gradle/caches/8.12/transforms/a6122aeba2a1a02fd3dd26be627ae248/transformed/espresso-core-3.5.1/jars/classes.jar"
      resolved="androidx.test.espresso:espresso-core:3.5.1"
      folder="/Users/<USER>/.gradle/caches/8.12/transforms/a6122aeba2a1a02fd3dd26be627ae248/transformed/espresso-core-3.5.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.test:core:1.5.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.12/transforms/d156d478fe7fb4992c78af27bc6dae2c/transformed/jetified-core-1.5.0/jars/classes.jar"
      resolved="androidx.test:core:1.5.0"
      folder="/Users/<USER>/.gradle/caches/8.12/transforms/d156d478fe7fb4992c78af27bc6dae2c/transformed/jetified-core-1.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.test:runner:1.5.2@aar"
      jars="/Users/<USER>/.gradle/caches/8.12/transforms/3e75369d8ff79e639c5bbc8bf9817452/transformed/runner-1.5.2/jars/classes.jar"
      resolved="androidx.test:runner:1.5.2"
      folder="/Users/<USER>/.gradle/caches/8.12/transforms/3e75369d8ff79e639c5bbc8bf9817452/transformed/runner-1.5.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.test.services:storage:1.4.2@aar"
      jars="/Users/<USER>/.gradle/caches/8.12/transforms/21ef4ac3692d4c97ad48d568f5eeb2cd/transformed/jetified-storage-1.4.2/jars/classes.jar"
      resolved="androidx.test.services:storage:1.4.2"
      folder="/Users/<USER>/.gradle/caches/8.12/transforms/21ef4ac3692d4c97ad48d568f5eeb2cd/transformed/jetified-storage-1.4.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.test:monitor:1.6.1@aar"
      jars="/Users/<USER>/.gradle/caches/8.12/transforms/bf94f200f60f555abb1cc8d1089b5194/transformed/monitor-1.6.1/jars/classes.jar"
      resolved="androidx.test:monitor:1.6.1"
      folder="/Users/<USER>/.gradle/caches/8.12/transforms/bf94f200f60f555abb1cc8d1089b5194/transformed/monitor-1.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.test:annotation:1.0.1@aar"
      jars="/Users/<USER>/.gradle/caches/8.12/transforms/3115a71df06179c7cd1710a404d7ce59/transformed/jetified-annotation-1.0.1/jars/classes.jar"
      resolved="androidx.test:annotation:1.0.1"
      folder="/Users/<USER>/.gradle/caches/8.12/transforms/3115a71df06179c7cd1710a404d7ce59/transformed/jetified-annotation-1.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.hamcrest:hamcrest-integration:1.3@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.hamcrest/hamcrest-integration/1.3/5de0c73fef18917cd85d0ab70bb23818685e4dfd/hamcrest-integration-1.3.jar"
      resolved="org.hamcrest:hamcrest-integration:1.3"/>
  <library
      name="org.hamcrest:hamcrest-library:1.3@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.hamcrest/hamcrest-library/1.3/4785a3c21320980282f9f33d0d1264a69040538f/hamcrest-library-1.3.jar"
      resolved="org.hamcrest:hamcrest-library:1.3"/>
  <library
      name="androidx.tracing:tracing:1.0.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.12/transforms/506c27cdfd10f86a4453bebc87767a02/transformed/jetified-tracing-1.0.0/jars/classes.jar"
      resolved="androidx.tracing:tracing:1.0.0"
      folder="/Users/<USER>/.gradle/caches/8.12/transforms/506c27cdfd10f86a4453bebc87767a02/transformed/jetified-tracing-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.code.findbugs:jsr305:2.0.2@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.google.code.findbugs/jsr305/2.0.2/516c03b21d50a644d538de0f0369c620989cd8f0/jsr305-2.0.2.jar"
      resolved="com.google.code.findbugs:jsr305:2.0.2"/>
  <library
      name="androidx.test.espresso:espresso-idling-resource:3.5.1@aar"
      jars="/Users/<USER>/.gradle/caches/8.12/transforms/c685f348077772dd47eb3fa74e30ca4e/transformed/espresso-idling-resource-3.5.1/jars/classes.jar"
      resolved="androidx.test.espresso:espresso-idling-resource:3.5.1"
      folder="/Users/<USER>/.gradle/caches/8.12/transforms/c685f348077772dd47eb3fa74e30ca4e/transformed/espresso-idling-resource-3.5.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.squareup:javawriter:2.1.1@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.squareup/javawriter/2.1.1/67ff45d9ae02e583d0f9b3432a5ebbe05c30c966/javawriter-2.1.1.jar"
      resolved="com.squareup:javawriter:2.1.1"/>
  <library
      name="javax.inject:javax.inject:1@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/javax.inject/javax.inject/1/6975da39a7040257bd51d21a231b76c915872d38/javax.inject-1.jar"
      resolved="javax.inject:javax.inject:1"/>
</libraries>
